import subprocess
import sys
import os

def build_executable():
    """
    main_turkish.py dosyasından çalıştırılabilir (exe) dosya oluşturur.
    """
    try:
        print("PyInstaller yükleniyor (eğ<PERSON> yü<PERSON><PERSON>ü <PERSON>)...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("PyInstaller başarıyla yüklendi.")

        print("Çalıştırılabilir dosya oluşturuluyor...")
        # main_turkish.py dosyasının yolunu doğru bir şekilde belirtin
        script_path = os.path.join(os.path.dirname(__file__), 'main_turkish.py')
        
        # PyInstaller komutu
        # --noconsole: Konsol penceresini göstermez
        # --onefile: <PERSON><PERSON> bir çalıştırılabilir dosya oluşturur
        # --icon: Uygulama ikonu (isteğe bağlı, .ico formatında olmalı)
        # --name: Çalıştırılabilir dosyanın adı
        command = [
            sys.executable, "-m", "PyInstaller",
            "--noconsole",
            "--onefile",
            "--name", "FiatStiloPro_TR",
            script_path
        ]
        
        subprocess.run(command, check=True)
        print("\nÇalıştırılabilir dosya başarıyla oluşturuldu!")
        print("Dosya 'dist' klasöründe bulunabilir.")
        print("Örnek: dist/FiatStiloPro_TR.exe")

    except subprocess.CalledProcessError as e:
        print(f"Hata: Çalıştırılabilir dosya oluşturulurken bir sorun oluştu. Hata kodu: {e.returncode}")
        print("PyInstaller çıktısı için yukarıdaki mesajları kontrol edin.")
        print(f"Detay: {e}")
    except FileNotFoundError:
        print("Hata: 'PyInstaller' komutu bulunamadı. PyInstaller'ın yüklü olduğundan emin olun.")
    except Exception as e:
        print(f"Beklenmeyen bir hata oluştu: {e}")

if __name__ == "__main__":
    build_executable()
