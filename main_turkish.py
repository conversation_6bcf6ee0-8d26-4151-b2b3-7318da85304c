import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sqlite3
from datetime import datetime, timedelta
import os
import json
import threading

# <PERSON><PERSON>t<PERSON><PERSON><PERSON> kontrolleri
try:
    from PIL import Image, ImageTk
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    import numpy as np
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

try:
    from tkcalendar import DateEntry
    CALENDAR_AVAILABLE = True
except ImportError:
    CALENDAR_AVAILABLE = False

try:
    import qrcode
    QR_AVAILABLE = True
except ImportError:
    QR_AVAILABLE = False

class FiatStiloTurkish:
    def __init__(self, root):
        self.root = root
        self.root.title("🚗 Fiat Stilo Pro - Türkçe Araç Takip Sistemi")
        self.root.geometry("1600x1000")
        self.root.minsize(1400, 900)
        
        # Modern renkler ve temalar
        self.themes = {
            'koyu': {
                'bg_primary': '#0D1117',      # Ana arka plan
                'bg_secondary': '#161B22',    # İkincil arka plan
                'bg_tertiary': '#21262D',     # Üçüncül arka plan
                'accent_blue': '#58A6FF',     # Mavi vurgu
                'accent_green': '#3FB950',    # Yeşil vurgu
                'accent_orange': '#FF8C42',   # Turuncu vurgu
                'accent_red': '#F85149',      # Kırmızı vurgu
                'accent_purple': '#BC8CFF',   # Mor vurgu
                'text_primary': '#F0F6FC',    # Ana metin
                'text_secondary': '#8B949E',  # İkincil metin
                'border': '#30363D',          # Kenarlık
                'hover': '#262C36'            # Hover efekti
            },
            'acik': {
                'bg_primary': '#FFFFFF',      # Beyaz
                'bg_secondary': '#F6F8FA',    # Açık gri
                'bg_tertiary': '#EAEEF2',     # Orta gri
                'accent_blue': '#0969DA',     # Mavi vurgu
                'accent_green': '#1A7F37',    # Yeşil vurgu
                'accent_orange': '#D1242F',   # Turuncu vurgu
                'accent_red': '#CF222E',      # Kırmızı vurgu
                'accent_purple': '#8250DF',   # Mor vurgu
                'text_primary': '#24292F',    # Ana metin
                'text_secondary': '#656D76',  # İkincil metin
                'border': '#D0D7DE',          # Kenarlık
                'hover': '#F3F4F6'            # Hover efekti
            }
        }
        
        self.current_theme = 'koyu'
        self.colors = self.themes[self.current_theme]
        
        # Modern fontlar
        self.fonts = {
            'title': ('Segoe UI', 24, 'bold'),
            'heading': ('Segoe UI', 18, 'bold'),
            'subheading': ('Segoe UI', 14, 'bold'),
            'body': ('Segoe UI', 11),
            'small': ('Segoe UI', 9),
            'button': ('Segoe UI', 10, 'bold')
        }
        
        # Veritabanını başlat
        self.init_database()
        
        # Modern arayüzü oluştur
        self.create_modern_ui()
        
        # Başlangıç verilerini yükle
        self.load_initial_data()
        
        # Otomatik yedekleme başlat
        self.start_auto_backup()
    
    def init_database(self):
        """Veritabanını başlat"""
        self.conn = sqlite3.connect('fiat_stilo_turkish.db')
        self.cursor = self.conn.cursor()
        
        # Araçlar tablosu
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS vehicles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                plate TEXT UNIQUE NOT NULL,
                model_year INTEGER,
                engine_type TEXT,
                fuel_type TEXT,
                color TEXT,
                purchase_date TEXT,
                current_km INTEGER,
                insurance_date TEXT,
                inspection_date TEXT,
                notes TEXT,
                photo_path TEXT,
                created_date TEXT,
                updated_date TEXT
            )
        ''')
        
        # Bakım tablosu
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS maintenance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                vehicle_plate TEXT,
                service_type TEXT,
                service_date TEXT,
                km_at_service INTEGER,
                cost REAL,
                service_location TEXT,
                parts_changed TEXT,
                next_service_km INTEGER,
                next_service_date TEXT,
                priority TEXT,
                status TEXT,
                notes TEXT,
                created_date TEXT,
                FOREIGN KEY (vehicle_plate) REFERENCES vehicles (plate)
            )
        ''')
        
        # Yakıt tablosu
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS fuel_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                vehicle_plate TEXT,
                fuel_date TEXT,
                km_at_fuel INTEGER,
                liters REAL,
                cost_per_liter REAL,
                total_cost REAL,
                fuel_station TEXT,
                fuel_type TEXT,
                notes TEXT,
                created_date TEXT,
                FOREIGN KEY (vehicle_plate) REFERENCES vehicles (plate)
            )
        ''')
        
        # Hatırlatmalar tablosu
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS reminders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                vehicle_plate TEXT,
                reminder_type TEXT,
                title TEXT,
                description TEXT,
                due_date TEXT,
                due_km INTEGER,
                priority TEXT,
                status TEXT,
                created_date TEXT,
                FOREIGN KEY (vehicle_plate) REFERENCES vehicles (plate)
            )
        ''')
        
        # Ayarlar tablosu
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_name TEXT UNIQUE,
                setting_value TEXT,
                updated_date TEXT
            )
        ''')
        
        self.conn.commit()
    
    def create_modern_ui(self):
        """Modern arayüz oluştur"""
        # Ana container
        self.root.configure(bg=self.colors['bg_primary'])
        
        # Üst header bar
        self.create_header()
        
        # Ana içerik alanı
        self.main_container = tk.Frame(self.root, bg=self.colors['bg_primary'])
        self.main_container.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Sol navigation panel
        self.create_navigation()
        
        # Sağ content area
        self.create_content_area()
        
        # Alt status bar
        self.create_status_bar()
        
        # Varsayılan dashboard göster
        self.show_dashboard()
    
    def create_header(self):
        """Header bar oluştur"""
        header = tk.Frame(self.root, bg=self.colors['bg_secondary'], height=80)
        header.pack(fill='x', padx=20, pady=(20, 0))
        header.pack_propagate(False)
        
        # Sol taraf - Logo ve başlık
        left_section = tk.Frame(header, bg=self.colors['bg_secondary'])
        left_section.pack(side='left', fill='y', padx=30)
        
        title_frame = tk.Frame(left_section, bg=self.colors['bg_secondary'])
        title_frame.pack(expand=True)
        
        logo_label = tk.Label(title_frame,
                             text="🚗",
                             font=('Segoe UI', 32),
                             bg=self.colors['bg_secondary'],
                             fg=self.colors['accent_blue'])
        logo_label.pack(side='left', padx=(0, 15))
        
        title_container = tk.Frame(title_frame, bg=self.colors['bg_secondary'])
        title_container.pack(side='left', fill='y')
        
        main_title = tk.Label(title_container,
                             text="FIAT STILO PRO",
                             font=self.fonts['title'],
                             bg=self.colors['bg_secondary'],
                             fg=self.colors['text_primary'])
        main_title.pack(anchor='w')
        
        subtitle = tk.Label(title_container,
                           text="Türkçe Araç Yönetim Sistemi",
                           font=self.fonts['small'],
                           bg=self.colors['bg_secondary'],
                           fg=self.colors['text_secondary'])
        subtitle.pack(anchor='w')
        
        # Sağ taraf - Kontroller
        right_section = tk.Frame(header, bg=self.colors['bg_secondary'])
        right_section.pack(side='right', fill='y', padx=30)
        
        controls_frame = tk.Frame(right_section, bg=self.colors['bg_secondary'])
        controls_frame.pack(expand=True)
        
        # Tema değiştirici
        theme_btn = self.create_button(
            controls_frame,
            "🌙" if self.current_theme == 'koyu' else "☀️",
            self.toggle_theme,
            self.colors['accent_purple'],
            width=50
        )
        theme_btn.pack(side='right', padx=5)
        
        # Bildirimler
        notif_btn = self.create_button(
            controls_frame,
            "🔔",
            self.show_notifications,
            self.colors['accent_orange'],
            width=50
        )
        notif_btn.pack(side='right', padx=5)
        
        # Hızlı arama
        search_frame = tk.Frame(controls_frame, bg=self.colors['bg_tertiary'])
        search_frame.pack(side='right', padx=10)
        
        self.search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame,
                               textvariable=self.search_var,
                               font=self.fonts['body'],
                               bg=self.colors['bg_tertiary'],
                               fg=self.colors['text_primary'],
                               relief='flat',
                               bd=0,
                               width=25)
        search_entry.pack(side='left', padx=10, pady=10)
        search_entry.bind('<Return>', self.quick_search)
        
        search_icon = tk.Label(search_frame,
                              text="🔍",
                              bg=self.colors['bg_tertiary'],
                              fg=self.colors['text_secondary'])
        search_icon.pack(side='right', padx=10)
    
    def create_navigation(self):
        """Navigation panel oluştur"""
        nav_panel = tk.Frame(self.main_container, 
                            bg=self.colors['bg_secondary'], 
                            width=300)
        nav_panel.pack(side='left', fill='y', padx=(0, 20))
        nav_panel.pack_propagate(False)
        
        # Navigation başlığı
        nav_header = tk.Frame(nav_panel, bg=self.colors['bg_secondary'])
        nav_header.pack(fill='x', padx=20, pady=20)
        
        nav_title = tk.Label(nav_header,
                            text="MENÜ",
                            font=self.fonts['subheading'],
                            bg=self.colors['bg_secondary'],
                            fg=self.colors['text_secondary'])
        nav_title.pack()
        
        # Navigation menü öğeleri
        self.nav_items = [
            ("📊", "Kontrol Paneli", self.show_dashboard, self.colors['accent_blue']),
            ("🚗", "Araç Yönetimi", self.show_vehicles, self.colors['accent_green']),
            ("🔧", "Bakım Kayıtları", self.show_maintenance, self.colors['accent_orange']),
            ("⛽", "Yakıt Kayıtları", self.show_fuel, self.colors['accent_red']),
            ("📈", "Analitik", self.show_analytics, self.colors['accent_purple']),
            ("📋", "Raporlar", self.show_reports, self.colors['accent_blue']),
            ("🔔", "Hatırlatmalar", self.show_reminders, self.colors['accent_red']),
            ("⚙️", "Ayarlar", self.show_settings, self.colors['text_secondary'])
        ]
        
        self.nav_buttons = {}
        for icon, text, command, color in self.nav_items:
            btn_container = tk.Frame(nav_panel, bg=self.colors['bg_secondary'])
            btn_container.pack(fill='x', padx=15, pady=3)
            
            btn = self.create_nav_button(btn_container, icon, text, command, color)
            self.nav_buttons[text] = btn
    
    def create_nav_button(self, parent, icon, text, command, color):
        """Navigation butonu oluştur"""
        btn_frame = tk.Frame(parent, bg=self.colors['bg_secondary'])
        btn_frame.pack(fill='x')
        
        btn = tk.Button(btn_frame,
                       text=f"{icon}  {text}",
                       command=command,
                       font=self.fonts['button'],
                       bg=self.colors['bg_secondary'],
                       fg=self.colors['text_primary'],
                       relief='flat',
                       bd=0,
                       anchor='w',
                       padx=20,
                       pady=15)
        btn.pack(fill='x')
        
        # Hover efekleri
        def on_enter(e):
            btn.configure(bg=self.colors['hover'], fg=color)
        
        def on_leave(e):
            btn.configure(bg=self.colors['bg_secondary'], fg=self.colors['text_primary'])
        
        def on_click(e):
            btn.configure(bg=color, fg='white')
            self.root.after(100, lambda: btn.configure(bg=self.colors['hover'], fg=color))
        
        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)
        btn.bind("<Button-1>", on_click)
        
        return btn
    
    def create_content_area(self):
        """Content area oluştur"""
        self.content_area = tk.Frame(self.main_container, bg=self.colors['bg_primary'])
        self.content_area.pack(side='right', fill='both', expand=True)
        
        # Content header
        self.content_header = tk.Frame(self.content_area, 
                                      bg=self.colors['bg_secondary'], 
                                      height=70)
        self.content_header.pack(fill='x', pady=(0, 20))
        self.content_header.pack_propagate(False)
        
        header_content = tk.Frame(self.content_header, bg=self.colors['bg_secondary'])
        header_content.pack(fill='both', expand=True, padx=30, pady=20)
        
        self.page_title = tk.Label(header_content,
                                  text="Kontrol Paneli",
                                  font=self.fonts['heading'],
                                  bg=self.colors['bg_secondary'],
                                  fg=self.colors['text_primary'])
        self.page_title.pack(side='left')
        
        # Breadcrumb
        self.breadcrumb = tk.Label(header_content,
                                  text="Ana Sayfa > Kontrol Paneli",
                                  font=self.fonts['small'],
                                  bg=self.colors['bg_secondary'],
                                  fg=self.colors['text_secondary'])
        self.breadcrumb.pack(side='right')
        
        # Main content frame
        self.content_frame = tk.Frame(self.content_area, bg=self.colors['bg_primary'])
        self.content_frame.pack(fill='both', expand=True)
    
    def create_status_bar(self):
        """Status bar oluştur"""
        status_bar = tk.Frame(self.root, bg=self.colors['bg_secondary'], height=40)
        status_bar.pack(fill='x', padx=20, pady=(10, 20))
        status_bar.pack_propagate(False)
        
        status_content = tk.Frame(status_bar, bg=self.colors['bg_secondary'])
        status_content.pack(fill='both', expand=True, padx=20, pady=10)
        
        self.status_label = tk.Label(status_content,
                                    text="Hazır",
                                    font=self.fonts['small'],
                                    bg=self.colors['bg_secondary'],
                                    fg=self.colors['text_secondary'])
        self.status_label.pack(side='left')
        
        time_label = tk.Label(status_content,
                             text=datetime.now().strftime("%H:%M - %d.%m.%Y"),
                             font=self.fonts['small'],
                             bg=self.colors['bg_secondary'],
                             fg=self.colors['text_secondary'])
        time_label.pack(side='right')
    
    def create_button(self, parent, text, command, color, width=None):
        """Modern buton oluştur"""
        btn = tk.Button(parent,
                       text=text,
                       command=command,
                       font=self.fonts['button'],
                       bg=color,
                       fg='white',
                       relief='flat',
                       bd=0,
                       padx=20,
                       pady=10)
        
        if width:
            btn.configure(width=width)
        
        # Hover efekti
        def on_enter(e):
            btn.configure(bg=self.lighten_color(color))
        
        def on_leave(e):
            btn.configure(bg=color)
        
        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)
        
        return btn
    
    def create_card(self, parent, title, content=None, color=None):
        """Modern kart oluştur"""
        card = tk.Frame(parent, bg=self.colors['bg_secondary'], relief='flat', bd=1)
        
        # Kart başlığı
        if title:
            header = tk.Frame(card, bg=color or self.colors['accent_blue'], height=40)
            header.pack(fill='x')
            header.pack_propagate(False)
            
            title_label = tk.Label(header,
                                  text=title,
                                  font=self.fonts['subheading'],
                                  bg=color or self.colors['accent_blue'],
                                  fg='white')
            title_label.pack(expand=True)
        
        # Kart içeriği
        content_frame = tk.Frame(card, bg=self.colors['bg_secondary'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        return card, content_frame
    
    def show_dashboard(self):
        """Kontrol paneli göster"""
        self.clear_content()
        self.page_title.configure(text="Kontrol Paneli")
        self.breadcrumb.configure(text="Ana Sayfa > Kontrol Paneli")
        
        # Dashboard container
        dashboard = tk.Frame(self.content_frame, bg=self.colors['bg_primary'])
        dashboard.pack(fill='both', expand=True, padx=20)
        
        # Üst istatistik kartları
        stats_frame = tk.Frame(dashboard, bg=self.colors['bg_primary'])
        stats_frame.pack(fill='x', pady=(0, 30))
        
        # İstatistikleri al
        stats = self.get_dashboard_stats()
        
        # Stat kartları
        stat_cards = [
            ("Toplam Araç", stats['vehicles'], "🚗", self.colors['accent_blue']),
            ("Bu Ay Bakım", stats['maintenance'], "🔧", self.colors['accent_green']),
            ("Toplam Maliyet", f"₺{stats['cost']:,.0f}", "💰", self.colors['accent_orange']),
            ("Uyarılar", stats['alerts'], "🔔", self.colors['accent_red'])
        ]
        
        for i, (title, value, icon, color) in enumerate(stat_cards):
            card = self.create_stat_card(stats_frame, title, value, icon, color)
            card.pack(side='left', fill='both', expand=True, padx=10)
        
        # Alt panel - Grafikler ve aktiviteler
        bottom_frame = tk.Frame(dashboard, bg=self.colors['bg_primary'])
        bottom_frame.pack(fill='both', expand=True)
        
        # Sol - Grafik
        chart_card, chart_content = self.create_card(bottom_frame, "Aylık Giderler", color=self.colors['accent_purple'])
        chart_card.pack(side='left', fill='both', expand=True, padx=(0, 15))
        
        self.create_expense_chart(chart_content)
        
        # Sağ - Son aktiviteler
        activity_card, activity_content = self.create_card(bottom_frame, "Son Aktiviteler", color=self.colors['accent_green'])
        activity_card.pack(side='right', fill='y', padx=(15, 0))
        activity_card.configure(width=400)
        
        self.create_activity_list(activity_content)
    
    def create_stat_card(self, parent, title, value, icon, color):
        """İstatistik kartı oluştur"""
        card = tk.Frame(parent, bg=self.colors['bg_secondary'], relief='flat', bd=0)
        
        # Gradient efekti için üst çizgi
        top_line = tk.Frame(card, bg=color, height=4)
        top_line.pack(fill='x')
        
        # Kart içeriği
        content = tk.Frame(card, bg=self.colors['bg_secondary'])
        content.pack(fill='both', expand=True, padx=25, pady=25)
        
        # İkon
        icon_label = tk.Label(content,
                             text=icon,
                             font=('Segoe UI', 36),
                             bg=self.colors['bg_secondary'],
                             fg=color)
        icon_label.pack(pady=(0, 10))
        
        # Değer
        value_label = tk.Label(content,
                              text=str(value),
                              font=('Segoe UI', 28, 'bold'),
                              bg=self.colors['bg_secondary'],
                              fg=self.colors['text_primary'])
        value_label.pack()
        
        # Başlık
        title_label = tk.Label(content,
                              text=title,
                              font=self.fonts['body'],
                              bg=self.colors['bg_secondary'],
                              fg=self.colors['text_secondary'])
        title_label.pack(pady=(5, 0))
        
        return card
    
    def show_vehicles(self):
        """Araç yönetimi göster"""
        self.clear_content()
        self.page_title.configure(text="Araç Yönetimi")
        self.breadcrumb.configure(text="Ana Sayfa > Araç Yönetimi")
        
        # Ana container
        vehicles_frame = tk.Frame(self.content_frame, bg=self.colors['bg_primary'])
        vehicles_frame.pack(fill='both', expand=True, padx=20)
        
        # Üst toolbar
        toolbar = tk.Frame(vehicles_frame, bg=self.colors['bg_secondary'], height=60)
        toolbar.pack(fill='x', pady=(0, 20))
        toolbar.pack_propagate(False)
        
        toolbar_content = tk.Frame(toolbar, bg=self.colors['bg_secondary'])
        toolbar_content.pack(fill='both', expand=True, padx=20, pady=15)
        
        # Sol taraf - Başlık
        tk.Label(toolbar_content,
                text="Araçlarınızı Yönetin",
                font=self.fonts['subheading'],
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_primary']).pack(side='left')
        
        # Sağ taraf - Butonlar
        btn_frame = tk.Frame(toolbar_content, bg=self.colors['bg_secondary'])
        btn_frame.pack(side='right')
        
        refresh_btn = self.create_button(btn_frame, "🔄 Yenile", 
                                        self.refresh_vehicles, 
                                        self.colors['accent_blue'])
        refresh_btn.pack(side='right', padx=5)
        
        # Ana içerik - Sol form, sağ liste
        content_container = tk.Frame(vehicles_frame, bg=self.colors['bg_primary'])
        content_container.pack(fill='both', expand=True)
        
        # Sol panel - Form
        form_card, form_content = self.create_card(content_container, "Araç Bilgileri", 
                                                  color=self.colors['accent_blue'])
        form_card.pack(side='left', fill='both', expand=True, padx=(0, 15))
        
        self.create_vehicle_form(form_content)
        
        # Sağ panel - Liste
        list_card, list_content = self.create_card(content_container, "Araç Listesi", 
                                                  color=self.colors['accent_green'])
        list_card.pack(side='right', fill='both', expand=True, padx=(15, 0))
        list_card.configure(width=500)
        
        self.create_vehicle_list(list_content)
    
    def create_vehicle_form(self, parent):
        """Araç formu oluştur"""
        # Form alanları
        self.vehicle_entries = {}
        
        fields = [
            ("Plaka *", "plate", "entry"),
            ("Model Yılı", "model_year", "entry"),
            ("Motor Tipi", "engine_type", "combo"),
            ("Yakıt Tipi", "fuel_type", "combo"),
            ("Renk", "color", "entry"),
            ("Mevcut KM", "current_km", "entry"),
            ("Alış Tarihi", "purchase_date", "date"),
            ("Sigorta Tarihi", "insurance_date", "date"),
            ("Muayene Tarihi", "inspection_date", "date")
        ]
        
        # 3 sütunlu layout
        for i, (label_text, field_name, field_type) in enumerate(fields):
            row = i // 3
            col = i % 3
            
            if col == 0:
                row_frame = tk.Frame(parent, bg=self.colors['bg_secondary'])
                row_frame.pack(fill='x', pady=10)
            
            field_frame = tk.Frame(row_frame, bg=self.colors['bg_secondary'])
            field_frame.pack(side='left', fill='x', expand=True, padx=10)
            
            # Label
            label = tk.Label(field_frame,
                           text=label_text,
                           font=self.fonts['small'],
                           bg=self.colors['bg_secondary'],
                           fg=self.colors['text_secondary'])
            label.pack(anchor='w', pady=(0, 5))
            
            # Entry
            if field_type == "entry":
                entry = tk.Entry(field_frame,
                               font=self.fonts['body'],
                               bg=self.colors['bg_tertiary'],
                               fg=self.colors['text_primary'],
                               relief='flat',
                               bd=0)
            elif field_type == "combo":
                if field_name == "engine_type":
                    values = ["1.2 16V", "1.4 16V", "1.6 16V", "1.8 16V", "1.9 JTD", "2.4 20V"]
                elif field_name == "fuel_type":
                    values = ["Benzin", "Dizel", "LPG", "Hibrit"]
                else:
                    values = []
                
                entry = ttk.Combobox(field_frame, values=values, font=self.fonts['body'])
            elif field_type == "date":
                if CALENDAR_AVAILABLE:
                    entry = DateEntry(field_frame,
                                    background=self.colors['accent_blue'],
                                    foreground='white',
                                    borderwidth=0,
                                    font=self.fonts['body'],
                                    date_pattern='dd.mm.yyyy')
                else:
                    entry = tk.Entry(field_frame,
                                   font=self.fonts['body'],
                                   bg=self.colors['bg_tertiary'],
                                   fg=self.colors['text_primary'],
                                   relief='flat',
                                   bd=0)
                    entry.insert(0, datetime.now().strftime('%d.%m.%Y'))
            
            entry.pack(fill='x', pady=(0, 5))
            self.vehicle_entries[field_name] = entry
        
        # Notlar alanı
        notes_frame = tk.Frame(parent, bg=self.colors['bg_secondary'])
        notes_frame.pack(fill='x', pady=15)
        
        tk.Label(notes_frame,
                text="Notlar",
                font=self.fonts['small'],
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_secondary']).pack(anchor='w', pady=(0, 5))
        
        self.vehicle_notes = tk.Text(notes_frame,
                                   height=4,
                                   font=self.fonts['body'],
                                   bg=self.colors['bg_tertiary'],
                                   fg=self.colors['text_primary'],
                                   relief='flat',
                                   bd=0)
        self.vehicle_notes.pack(fill='x')
        
        # Butonlar
        button_frame = tk.Frame(parent, bg=self.colors['bg_secondary'])
        button_frame.pack(fill='x', pady=20)
        
        buttons = [
            ("💾 Kaydet", self.save_vehicle, self.colors['accent_green']),
            ("✏️ Güncelle", self.update_vehicle, self.colors['accent_orange']),
            ("🗑️ Sil", self.delete_vehicle, self.colors['accent_red']),
            ("🔄 Temizle", self.clear_vehicle_form, self.colors['text_secondary'])
        ]
        
        for text, command, color in buttons:
            btn = self.create_button(button_frame, text, command, color)
            btn.pack(side='left', padx=5)
    
    def create_vehicle_list(self, parent):
        """Araç listesi oluştur"""
        # Treeview
        columns = ('Plaka', 'Yıl', 'Motor', 'KM')
        self.vehicle_tree = ttk.Treeview(parent, columns=columns, show='headings', height=20)
        
        # Sütun başlıkları
        for col in columns:
            self.vehicle_tree.heading(col, text=col)
            self.vehicle_tree.column(col, width=100)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(parent, orient='vertical', command=self.vehicle_tree.yview)
        self.vehicle_tree.configure(yscrollcommand=scrollbar.set)
        
        self.vehicle_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # Çift tıklama eventi
        self.vehicle_tree.bind('<Double-1>', self.load_vehicle_for_edit)
        
        # Verileri yükle
        self.load_vehicle_data()
    
    def show_maintenance(self):
        """Bakım kayıtları göster"""
        self.clear_content()
        self.page_title.configure(text="Bakım Kayıtları")
        self.breadcrumb.configure(text="Ana Sayfa > Bakım Kayıtları")
        
        # Ana container
        maintenance_frame = tk.Frame(self.content_frame, bg=self.colors['bg_primary'])
        maintenance_frame.pack(fill='both', expand=True, padx=20)
        
        # Notebook
        notebook = ttk.Notebook(maintenance_frame)
        notebook.pack(fill='both', expand=True)
        
        # Yeni kayıt sekmesi
        new_tab = tk.Frame(notebook, bg=self.colors['bg_primary'])
        notebook.add(new_tab, text="Yeni Bakım Kaydı")
        
        # Kayıt listesi sekmesi
        list_tab = tk.Frame(notebook, bg=self.colors['bg_primary'])
        notebook.add(list_tab, text="Bakım Geçmişi")
        
        self.create_maintenance_form(new_tab)
        self.create_maintenance_list(list_tab)
    
    def create_maintenance_form(self, parent):
        """Bakım formu oluştur"""
        form_card, form_content = self.create_card(parent, "Yeni Bakım Kaydı", 
                                                  color=self.colors['accent_orange'])
        form_card.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Form alanları
        self.maintenance_entries = {}
        
        # İlk satır
        row1 = tk.Frame(form_content, bg=self.colors['bg_secondary'])
        row1.pack(fill='x', pady=10)
        
        fields_row1 = [
            ("Plaka", "plate", "combo"),
            ("Bakım Tipi", "service_type", "combo"),
            ("Tarih", "service_date", "date")
        ]
        
        for label_text, field_name, field_type in fields_row1:
            col_frame = tk.Frame(row1, bg=self.colors['bg_secondary'])
            col_frame.pack(side='left', fill='x', expand=True, padx=10)
            
            tk.Label(col_frame, text=label_text, font=self.fonts['small'],
                    bg=self.colors['bg_secondary'], fg=self.colors['text_secondary']).pack(anchor='w')
            
            if field_type == "combo":
                if field_name == "plate":
                    self.cursor.execute('SELECT DISTINCT plate FROM vehicles')
                    plates = [row[0] for row in self.cursor.fetchall()]
                    entry = ttk.Combobox(col_frame, values=plates)
                elif field_name == "service_type":
                    service_types = [
                        "Periyodik Bakım", "Yağ Değişimi", "Filtre Değişimi",
                        "Fren Bakımı", "Lastik Değişimi", "Akü Değişimi",
                        "Motor Bakımı", "Şanzıman Bakımı", "Klima Bakımı",
                        "Elektrik Bakımı", "Kaporta Onarımı", "Cam Değişimi",
                        "Egzoz Bakımı", "Süspansiyon Bakımı", "Diğer"
                    ]
                    entry = ttk.Combobox(col_frame, values=service_types)
            elif field_type == "date":
                if CALENDAR_AVAILABLE:
                    entry = DateEntry(col_frame, background=self.colors['accent_orange'],
                                    foreground='white', borderwidth=0, date_pattern='dd.mm.yyyy')
                    entry.set_date(datetime.now().date())
                else:
                    entry = tk.Entry(col_frame, font=self.fonts['body'],
                                   bg=self.colors['bg_tertiary'], fg=self.colors['text_primary'])
                    entry.insert(0, datetime.now().strftime('%d.%m.%Y'))
            
            entry.pack(fill='x', pady=5)
            self.maintenance_entries[field_name] = entry
        
        # İkinci satır
        row2 = tk.Frame(form_content, bg=self.colors['bg_secondary'])
        row2.pack(fill='x', pady=10)
        
        fields_row2 = [
            ("KM", "km_at_service"),
            ("Maliyet (₺)", "cost"),
            ("Servis Yeri", "service_location")
        ]
        
        for label_text, field_name in fields_row2:
            col_frame = tk.Frame(row2, bg=self.colors['bg_secondary'])
            col_frame.pack(side='left', fill='x', expand=True, padx=10)
            
            tk.Label(col_frame, text=label_text, font=self.fonts['small'],
                    bg=self.colors['bg_secondary'], fg=self.colors['text_secondary']).pack(anchor='w')
            
            entry = tk.Entry(col_frame, font=self.fonts['body'],
                           bg=self.colors['bg_tertiary'], fg=self.colors['text_primary'])
            entry.pack(fill='x', pady=5)
            self.maintenance_entries[field_name] = entry
        
        # Detaylar
        details_frame = tk.Frame(form_content, bg=self.colors['bg_secondary'])
        details_frame.pack(fill='x', pady=15)
        
        # Sol - Parçalar
        left_detail = tk.Frame(details_frame, bg=self.colors['bg_secondary'])
        left_detail.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        tk.Label(left_detail, text="Değiştirilen Parçalar:", font=self.fonts['small'],
                bg=self.colors['bg_secondary'], fg=self.colors['text_secondary']).pack(anchor='w')
        
        self.maintenance_parts = tk.Text(left_detail, height=4, font=self.fonts['body'],
                                       bg=self.colors['bg_tertiary'], fg=self.colors['text_primary'])
        self.maintenance_parts.pack(fill='both', expand=True, pady=5)
        
        # Sağ - Notlar
        right_detail = tk.Frame(details_frame, bg=self.colors['bg_secondary'])
        right_detail.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
        tk.Label(right_detail, text="Notlar:", font=self.fonts['small'],
                bg=self.colors['bg_secondary'], fg=self.colors['text_secondary']).pack(anchor='w')
        
        self.maintenance_notes = tk.Text(right_detail, height=4, font=self.fonts['body'],
                                       bg=self.colors['bg_tertiary'], fg=self.colors['text_primary'])
        self.maintenance_notes.pack(fill='both', expand=True, pady=5)
        
        # Butonlar
        button_frame = tk.Frame(form_content, bg=self.colors['bg_secondary'])
        button_frame.pack(fill='x', pady=20)
        
        save_btn = self.create_button(button_frame, "💾 Kaydet", self.save_maintenance, 
                                     self.colors['accent_green'])
        save_btn.pack(side='left', padx=5)
        
        clear_btn = self.create_button(button_frame, "🔄 Temizle", self.clear_maintenance_form, 
                                      self.colors['text_secondary'])
        clear_btn.pack(side='left', padx=5)
    
    def create_maintenance_list(self, parent):
        """Bakım listesi oluştur"""
        list_card, list_content = self.create_card(parent, "Bakım Geçmişi", 
                                                  color=self.colors['accent_green'])
        list_card.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Treeview
        columns = ('Tarih', 'Plaka', 'Bakım Tipi', 'KM', 'Maliyet', 'Servis Yeri')
        self.maintenance_tree = ttk.Treeview(list_content, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.maintenance_tree.heading(col, text=col)
            self.maintenance_tree.column(col, width=120)
        
        scrollbar_m = ttk.Scrollbar(list_content, orient='vertical', command=self.maintenance_tree.yview)
        self.maintenance_tree.configure(yscrollcommand=scrollbar_m.set)
        
        self.maintenance_tree.pack(side='left', fill='both', expand=True)
        scrollbar_m.pack(side='right', fill='y')
        
        self.load_maintenance_data()
    
    def show_fuel(self):
        """Yakıt kayıtları göster"""
        self.clear_content()
        self.page_title.configure(text="Yakıt Kayıtları")
        self.breadcrumb.configure(text="Ana Sayfa > Yakıt Kayıtları")
        
        # Ana container
        fuel_frame = tk.Frame(self.content_frame, bg=self.colors['bg_primary'])
        fuel_frame.pack(fill='both', expand=True, padx=20)
        
        # Notebook
        notebook = ttk.Notebook(fuel_frame)
        notebook.pack(fill='both', expand=True)
        
        # Yeni kayıt sekmesi
        new_tab = tk.Frame(notebook, bg=self.colors['bg_primary'])
        notebook.add(new_tab, text="Yeni Yakıt Kaydı")
        
        # Kayıt listesi sekmesi
        list_tab = tk.Frame(notebook, bg=self.colors['bg_primary'])
        notebook.add(list_tab, text="Yakıt Geçmişi")
        
        self.create_fuel_form(new_tab)
        self.create_fuel_list(list_tab)
    
    def create_fuel_form(self, parent):
        """Yakıt formu oluştur"""
        form_card, form_content = self.create_card(parent, "Yeni Yakıt Kaydı", 
                                                  color=self.colors['accent_red'])
        form_card.pack(fill='both', expand=True, padx=20, pady=20)
        
        self.fuel_entries = {}
        
        # Form alanları
        fields = [
            ("Plaka", "plate", "combo"),
            ("Tarih", "fuel_date", "date"),
            ("KM", "km_at_fuel", "entry"),
            ("Litre", "liters", "entry"),
            ("Litre Fiyatı (₺)", "cost_per_liter", "entry"),
            ("Toplam Tutar (₺)", "total_cost", "entry"),
            ("Benzin İstasyonu", "fuel_station", "entry"),
            ("Yakıt Tipi", "fuel_type", "combo")
        ]
        
        # 4x2 grid layout
        for i, (label_text, field_name, field_type) in enumerate(fields):
            row = i // 4
            col = i % 4
            
            if col == 0:
                row_frame = tk.Frame(form_content, bg=self.colors['bg_secondary'])
                row_frame.pack(fill='x', pady=10)
            
            col_frame = tk.Frame(row_frame, bg=self.colors['bg_secondary'])
            col_frame.pack(side='left', fill='x', expand=True, padx=5)
            
            tk.Label(col_frame, text=label_text, font=self.fonts['small'],
                    bg=self.colors['bg_secondary'], fg=self.colors['text_secondary']).pack(anchor='w')
            
            if field_type == "combo":
                if field_name == "plate":
                    self.cursor.execute('SELECT DISTINCT plate FROM vehicles')
                    plates = [row[0] for row in self.cursor.fetchall()]
                    entry = ttk.Combobox(col_frame, values=plates)
                elif field_name == "fuel_type":
                    entry = ttk.Combobox(col_frame, values=["Benzin", "Dizel", "LPG"])
            elif field_type == "date":
                if CALENDAR_AVAILABLE:
                    entry = DateEntry(col_frame, background=self.colors['accent_red'],
                                    foreground='white', borderwidth=0, date_pattern='dd.mm.yyyy')
                    entry.set_date(datetime.now().date())
                else:
                    entry = tk.Entry(col_frame, font=self.fonts['body'],
                                   bg=self.colors['bg_tertiary'], fg=self.colors['text_primary'])
                    entry.insert(0, datetime.now().strftime('%d.%m.%Y'))
            else:
                entry = tk.Entry(col_frame, font=self.fonts['body'],
                               bg=self.colors['bg_tertiary'], fg=self.colors['text_primary'])
            
            entry.pack(fill='x', pady=5)
            self.fuel_entries[field_name] = entry
        
        # Notlar
        notes_frame = tk.Frame(form_content, bg=self.colors['bg_secondary'])
        notes_frame.pack(fill='x', pady=15)
        
        tk.Label(notes_frame, text="Notlar:", font=self.fonts['small'],
                bg=self.colors['bg_secondary'], fg=self.colors['text_secondary']).pack(anchor='w')
        
        self.fuel_notes = tk.Text(notes_frame, height=3, font=self.fonts['body'],
                                bg=self.colors['bg_tertiary'], fg=self.colors['text_primary'])
        self.fuel_notes.pack(fill='x', pady=5)
        
        # Butonlar
        button_frame = tk.Frame(form_content, bg=self.colors['bg_secondary'])
        button_frame.pack(fill='x', pady=20)
        
        save_btn = self.create_button(button_frame, "💾 Kaydet", self.save_fuel, 
                                     self.colors['accent_green'])
        save_btn.pack(side='left', padx=5)
        
        clear_btn = self.create_button(button_frame, "🔄 Temizle", self.clear_fuel_form, 
                                      self.colors['text_secondary'])
        clear_btn.pack(side='left', padx=5)
    
    def create_fuel_list(self, parent):
        """Yakıt listesi oluştur"""
        list_card, list_content = self.create_card(parent, "Yakıt Geçmişi", 
                                                  color=self.colors['accent_orange'])
        list_card.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Treeview
        columns = ('Tarih', 'Plaka', 'KM', 'Litre', 'L.Fiyatı', 'Toplam', 'İstasyon')
        self.fuel_tree = ttk.Treeview(list_content, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.fuel_tree.heading(col, text=col)
            self.fuel_tree.column(col, width=100)
        
        scrollbar_f = ttk.Scrollbar(list_content, orient='vertical', command=self.fuel_tree.yview)
        self.fuel_tree.configure(yscrollcommand=scrollbar_f.set)
        
        self.fuel_tree.pack(side='left', fill='both', expand=True)
        scrollbar_f.pack(side='right', fill='y')
        
        self.load_fuel_data()
    
    def show_analytics(self):
        """Analitik göster"""
        self.clear_content()
        self.page_title.configure(text="Analitik")
        self.breadcrumb.configure(text="Ana Sayfa > Analitik")
        
        # Ana container
        analytics_frame = tk.Frame(self.content_frame, bg=self.colors['bg_primary'])
        analytics_frame.pack(fill='both', expand=True, padx=20)
        
        # Üst istatistikler
        stats_frame = tk.Frame(analytics_frame, bg=self.colors['bg_primary'])
        stats_frame.pack(fill='x', pady=(0, 20))
        
        # Analitik kartları
        analytics_stats = self.get_analytics_stats()
        
        stat_cards = [
            ("Ortalama Tüketim", f"{analytics_stats['avg_consumption']:.1f} L/100km", "⛽", self.colors['accent_red']),
            ("Aylık Ortalama", f"₺{analytics_stats['monthly_avg']:,.0f}", "📊", self.colors['accent_blue']),
            ("En Pahalı Bakım", f"₺{analytics_stats['max_maintenance']:,.0f}", "🔧", self.colors['accent_orange']),
            ("Toplam Mesafe", f"{analytics_stats['total_distance']:,} km", "🛣️", self.colors['accent_green'])
        ]
        
        for title, value, icon, color in stat_cards:
            card = self.create_stat_card(stats_frame, title, value, icon, color)
            card.pack(side='left', fill='both', expand=True, padx=10)
        
        # Alt grafikler
        charts_frame = tk.Frame(analytics_frame, bg=self.colors['bg_primary'])
        charts_frame.pack(fill='both', expand=True)
        
        # Sol - Yakıt tüketimi
        fuel_chart_card, fuel_chart_content = self.create_card(charts_frame, "Yakıt Tüketim Trendi", 
                                                              color=self.colors['accent_red'])
        fuel_chart_card.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        self.create_fuel_consumption_chart(fuel_chart_content)
        
        # Sağ - Maliyet dağılımı
        cost_chart_card, cost_chart_content = self.create_card(charts_frame, "Maliyet Dağılımı", 
                                                              color=self.colors['accent_purple'])
        cost_chart_card.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
        self.create_cost_distribution_chart(cost_chart_content)
    
    def show_reports(self):
        """Raporlar göster"""
        self.clear_content()
        self.page_title.configure(text="Raporlar")
        self.breadcrumb.configure(text="Ana Sayfa > Raporlar")
        
        # Ana container
        reports_frame = tk.Frame(self.content_frame, bg=self.colors['bg_primary'])
        reports_frame.pack(fill='both', expand=True, padx=20)
        
        # Rapor butonları
        buttons_frame = tk.Frame(reports_frame, bg=self.colors['bg_primary'])
        buttons_frame.pack(fill='x', pady=20)
        
        report_buttons = [
            ("📊 Bakım Özeti", self.generate_maintenance_report, self.colors['accent_orange']),
            ("⛽ Yakıt Raporu", self.generate_fuel_report, self.colors['accent_red']),
            ("💰 Maliyet Analizi", self.generate_cost_report, self.colors['accent_green']),
            ("📋 Genel Rapor", self.generate_general_report, self.colors['accent_blue']),
            ("📤 Excel'e Aktar", self.export_to_excel, self.colors['accent_purple']),
            ("🖨️ PDF Oluştur", self.export_to_pdf, self.colors['text_secondary'])
        ]
        
        for i, (text, command, color) in enumerate(report_buttons):
            if i % 3 == 0:
                row_frame = tk.Frame(buttons_frame, bg=self.colors['bg_primary'])
                row_frame.pack(fill='x', pady=10)
            
            btn = self.create_button(row_frame, text, command, color)
            btn.pack(side='left', padx=10, fill='x', expand=True)
        
        # Rapor sonuçları
        self.report_card, self.report_content = self.create_card(reports_frame, "Rapor Sonuçları", 
                                                                color=self.colors['accent_blue'])
        self.report_card.pack(fill='both', expand=True, pady=20)
        
        self.report_text = tk.Text(self.report_content, font=self.fonts['body'],
                                  bg=self.colors['bg_tertiary'], fg=self.colors['text_primary'],
                                  wrap='word')
        
        report_scroll = ttk.Scrollbar(self.report_content, orient='vertical', command=self.report_text.yview)
        self.report_text.configure(yscrollcommand=report_scroll.set)
        
        self.report_text.pack(side='left', fill='both', expand=True)
        report_scroll.pack(side='right', fill='y')
    
    def show_reminders(self):
        """Hatırlatmalar göster"""
        self.clear_content()
        self.page_title.configure(text="Hatırlatmalar")
        self.breadcrumb.configure(text="Ana Sayfa > Hatırlatmalar")
        
        # Ana container
        reminders_frame = tk.Frame(self.content_frame, bg=self.colors['bg_primary'])
        reminders_frame.pack(fill='both', expand=True, padx=20)
        
        # Üst toolbar
        toolbar = tk.Frame(reminders_frame, bg=self.colors['bg_secondary'], height=60)
        toolbar.pack(fill='x', pady=(0, 20))
        toolbar.pack_propagate(False)
        
        toolbar_content = tk.Frame(toolbar, bg=self.colors['bg_secondary'])
        toolbar_content.pack(fill='both', expand=True, padx=20, pady=15)
        
        tk.Label(toolbar_content, text="Hatırlatma Yönetimi", font=self.fonts['subheading'],
                bg=self.colors['bg_secondary'], fg=self.colors['text_primary']).pack(side='left')
        
        add_reminder_btn = self.create_button(toolbar_content, "+ Yeni Hatırlatma", 
                                             self.add_reminder_dialog, self.colors['accent_green'])
        add_reminder_btn.pack(side='right')
        
        # Ana içerik
        content_container = tk.Frame(reminders_frame, bg=self.colors['bg_primary'])
        content_container.pack(fill='both', expand=True)
        
        # Sol - Aktif hatırlatmalar
        active_card, active_content = self.create_card(content_container, "Aktif Hatırlatmalar", 
                                                      color=self.colors['accent_red'])
        active_card.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        self.create_active_reminders(active_content)
        
        # Sağ - Yaklaşan hatırlatmalar
        upcoming_card, upcoming_content = self.create_card(content_container, "Yaklaşan Hatırlatmalar", 
                                                          color=self.colors['accent_orange'])
        upcoming_card.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
        self.create_upcoming_reminders(upcoming_content)
    
    def show_settings(self):
        """Ayarlar göster"""
        self.clear_content()
        self.page_title.configure(text="Ayarlar")
        self.breadcrumb.configure(text="Ana Sayfa > Ayarlar")
        
        # Ana container
        settings_frame = tk.Frame(self.content_frame, bg=self.colors['bg_primary'])
        settings_frame.pack(fill='both', expand=True, padx=20)
        
        # Notebook
        notebook = ttk.Notebook(settings_frame)
        notebook.pack(fill='both', expand=True)
        
        # Genel ayarlar
        general_tab = tk.Frame(notebook, bg=self.colors['bg_primary'])
        notebook.add(general_tab, text="Genel Ayarlar")
        
        # Yedekleme
        backup_tab = tk.Frame(notebook, bg=self.colors['bg_primary'])
        notebook.add(backup_tab, text="Yedekleme")
        
        # Hakkında
        about_tab = tk.Frame(notebook, bg=self.colors['bg_primary'])
        notebook.add(about_tab, text="Hakkında")
        
        self.create_general_settings(general_tab)
        self.create_backup_settings(backup_tab)
        self.create_about_section(about_tab)
    
    # Veri işleme metodları
    def save_vehicle(self):
        """Araç kaydet"""
        try:
            plate = self.vehicle_entries['plate'].get().upper().strip()
            if not plate:
                messagebox.showerror("Hata", "Plaka alanı boş olamaz!")
                return
            
            # Tarih alanlarını kontrol et
            purchase_date = self.get_date_value('purchase_date')
            insurance_date = self.get_date_value('insurance_date')
            inspection_date = self.get_date_value('inspection_date')
            
            data = (
                plate,
                self.vehicle_entries['model_year'].get(),
                self.vehicle_entries['engine_type'].get(),
                self.vehicle_entries['fuel_type'].get(),
                self.vehicle_entries['color'].get(),
                purchase_date,
                self.vehicle_entries['current_km'].get(),
                insurance_date,
                inspection_date,
                self.vehicle_notes.get('1.0', 'end-1c'),
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            )
            
            self.cursor.execute('''
                INSERT INTO vehicles (plate, model_year, engine_type, fuel_type, color,
                                    purchase_date, current_km, insurance_date, inspection_date,
                                    notes, created_date, updated_date)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', data)
            
            self.conn.commit()
            messagebox.showinfo("Başarılı", "Araç başarıyla kaydedildi!")
            self.clear_vehicle_form()
            self.load_vehicle_data()
            self.update_status(f"Araç kaydedildi: {plate}")
            
        except sqlite3.IntegrityError:
            messagebox.showerror("Hata", "Bu plaka zaten kayıtlı!")
        except Exception as e:
            messagebox.showerror("Hata", f"Kayıt hatası: {str(e)}")
    
    def save_maintenance(self):
        """Bakım kaydet"""
        try:
            plate = self.maintenance_entries['plate'].get().upper().strip()
            if not plate:
                messagebox.showerror("Hata", "Plaka alanı boş olamaz!")
                return
            
            service_date = self.get_maintenance_date_value('service_date')
            
            data = (
                plate,
                self.maintenance_entries['service_type'].get(),
                service_date,
                self.maintenance_entries['km_at_service'].get(),
                self.maintenance_entries['cost'].get(),
                self.maintenance_entries['service_location'].get(),
                self.maintenance_parts.get('1.0', 'end-1c'),
                '',  # next_service_km
                '',  # next_service_date
                'Normal',  # priority
                'Tamamlandı',  # status
                self.maintenance_notes.get('1.0', 'end-1c'),
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            )
            
            self.cursor.execute('''
                INSERT INTO maintenance (vehicle_plate, service_type, service_date, km_at_service,
                                       cost, service_location, parts_changed, next_service_km,
                                       next_service_date, priority, status, notes, created_date)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', data)
            
            self.conn.commit()
            messagebox.showinfo("Başarılı", "Bakım kaydı başarıyla eklendi!")
            self.clear_maintenance_form()
            self.load_maintenance_data()
            self.update_status(f"Bakım kaydedildi: {plate}")
            
        except Exception as e:
            messagebox.showerror("Hata", f"Bakım kayıt hatası: {str(e)}")
    
    def save_fuel(self):
        """Yakıt kaydet"""
        try:
            plate = self.fuel_entries['plate'].get().upper().strip()
            if not plate:
                messagebox.showerror("Hata", "Plaka alanı boş olamaz!")
                return
            
            fuel_date = self.get_fuel_date_value('fuel_date')
            
            data = (
                plate,
                fuel_date,
                self.fuel_entries['km_at_fuel'].get(),
                self.fuel_entries['liters'].get(),
                self.fuel_entries['cost_per_liter'].get(),
                self.fuel_entries['total_cost'].get(),
                self.fuel_entries['fuel_station'].get(),
                self.fuel_entries['fuel_type'].get(),
                self.fuel_notes.get('1.0', 'end-1c'),
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            )
            
            self.cursor.execute('''
                INSERT INTO fuel_records (vehicle_plate, fuel_date, km_at_fuel, liters,
                                        cost_per_liter, total_cost, fuel_station, fuel_type,
                                        notes, created_date)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', data)
            
            self.conn.commit()
            messagebox.showinfo("Başarılı", "Yakıt kaydı başarıyla eklendi!")
            self.clear_fuel_form()
            self.load_fuel_data()
            self.update_status(f"Yakıt kaydedildi: {plate}")
            
        except Exception as e:
            messagebox.showerror("Hata", f"Yakıt kayıt hatası: {str(e)}")
    
    def get_date_value(self, field_name):
        """Tarih değerini al"""
        entry = self.vehicle_entries[field_name]
        if CALENDAR_AVAILABLE and hasattr(entry, 'get_date'):
            return entry.get_date().strftime('%Y-%m-%d')
        else:
            return entry.get()
    
    def get_maintenance_date_value(self, field_name):
        """Bakım tarih değerini al"""
        entry = self.maintenance_entries[field_name]
        if CALENDAR_AVAILABLE and hasattr(entry, 'get_date'):
            return entry.get_date().strftime('%Y-%m-%d')
        else:
            return entry.get()
    
    def get_fuel_date_value(self, field_name):
        """Yakıt tarih değerini al"""
        entry = self.fuel_entries[field_name]
        if CALENDAR_AVAILABLE and hasattr(entry, 'get_date'):
            return entry.get_date().strftime('%Y-%m-%d')
        else:
            return entry.get()
    
    def load_vehicle_data(self):
        """Araç verilerini yükle"""
        try:
            for item in self.vehicle_tree.get_children():
                self.vehicle_tree.delete(item)
            
            self.cursor.execute('SELECT plate, model_year, engine_type, current_km FROM vehicles ORDER BY plate')
            vehicles = self.cursor.fetchall()
            
            for vehicle in vehicles:
                self.vehicle_tree.insert('', 'end', values=vehicle)
                
        except Exception as e:
            print(f"Araç veri yükleme hatası: {e}")
    
    def load_maintenance_data(self):
        """Bakım verilerini yükle"""
        try:
            for item in self.maintenance_tree.get_children():
                self.maintenance_tree.delete(item)
            
            self.cursor.execute('''
                SELECT service_date, vehicle_plate, service_type, km_at_service, cost, service_location
                FROM maintenance ORDER BY service_date DESC
            ''')
            
            for row in self.cursor.fetchall():
                self.maintenance_tree.insert('', 'end', values=row)
                
        except Exception as e:
            print(f"Bakım veri yükleme hatası: {e}")
    
    def load_fuel_data(self):
        """Yakıt verilerini yükle"""
        try:
            for item in self.fuel_tree.get_children():
                self.fuel_tree.delete(item)
            
            self.cursor.execute('''
                SELECT fuel_date, vehicle_plate, km_at_fuel, liters, cost_per_liter, total_cost, fuel_station
                FROM fuel_records ORDER BY fuel_date DESC
            ''')
            
            for row in self.cursor.fetchall():
                self.fuel_tree.insert('', 'end', values=row)
                
        except Exception as e:
            print(f"Yakıt veri yükleme hatası: {e}")
    
    def load_vehicle_for_edit(self, event):
        """Düzenleme için araç yükle"""
        try:
            selection = self.vehicle_tree.selection()
            if selection:
                item = self.vehicle_tree.item(selection[0])
                plate = item['values'][0]
                
                self.cursor.execute('SELECT * FROM vehicles WHERE plate = ?', (plate,))
                vehicle = self.cursor.fetchone()
                
                if vehicle:
                    # Form alanlarını doldur
                    self.vehicle_entries['plate'].delete(0, 'end')
                    self.vehicle_entries['plate'].insert(0, vehicle[1])
                    
                    if vehicle[2]:
                        self.vehicle_entries['model_year'].delete(0, 'end')
                        self.vehicle_entries['model_year'].insert(0, str(vehicle[2]))
                    
                    if vehicle[3]:
                        self.vehicle_entries['engine_type'].set(vehicle[3])
                    
                    if vehicle[4]:
                        self.vehicle_entries['fuel_type'].set(vehicle[4])
                    
                    if vehicle[5]:
                        self.vehicle_entries['color'].delete(0, 'end')
                        self.vehicle_entries['color'].insert(0, vehicle[5])
                    
                    if vehicle[7]:
                        self.vehicle_entries['current_km'].delete(0, 'end')
                        self.vehicle_entries['current_km'].insert(0, str(vehicle[7]))
                    
                    # Tarih alanları
                    self.set_date_value('purchase_date', vehicle[6])
                    self.set_date_value('insurance_date', vehicle[8])
                    self.set_date_value('inspection_date', vehicle[9])
                    
                    # Notlar
                    if vehicle[10]:
                        self.vehicle_notes.delete('1.0', 'end')
                        self.vehicle_notes.insert('1.0', vehicle[10])
                    
        except Exception as e:
            messagebox.showerror("Hata", f"Yükleme hatası: {str(e)}")
    
    def set_date_value(self, field_name, date_str):
        """Tarih değerini ayarla"""
        if not date_str:
            return
            
        entry = self.vehicle_entries[field_name]
        if CALENDAR_AVAILABLE and hasattr(entry, 'set_date'):
            try:
                date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
                entry.set_date(date_obj)
            except:
                pass
        else:
            entry.delete(0, 'end')
            try:
                date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                entry.insert(0, date_obj.strftime('%d.%m.%Y'))
            except:
                entry.insert(0, date_str)
    
    def clear_vehicle_form(self):
        """Araç formunu temizle"""
        try:
            for field_name, entry in self.vehicle_entries.items():
                if hasattr(entry, 'delete'):
                    entry.delete(0, 'end')
                elif hasattr(entry, 'set'):
                    entry.set('')
                elif hasattr(entry, 'set_date') and CALENDAR_AVAILABLE:
                    entry.set_date(datetime.now().date())
            
            self.vehicle_notes.delete('1.0', 'end')
            self.update_status("Form temizlendi")
        except Exception as e:
            print(f"Form temizleme hatası: {e}")
    
    def clear_maintenance_form(self):
        """Bakım formunu temizle"""
        try:
            for field_name, entry in self.maintenance_entries.items():
                if hasattr(entry, 'delete'):
                    entry.delete(0, 'end')
                elif hasattr(entry, 'set'):
                    entry.set('')
                elif hasattr(entry, 'set_date') and CALENDAR_AVAILABLE:
                    entry.set_date(datetime.now().date())
            
            self.maintenance_parts.delete('1.0', 'end')
            self.maintenance_notes.delete('1.0', 'end')
            self.update_status("Bakım formu temizlendi")
        except Exception as e:
            print(f"Bakım form temizleme hatası: {e}")
    
    def clear_fuel_form(self):
        """Yakıt formunu temizle"""
        try:
            for field_name, entry in self.fuel_entries.items():
                if hasattr(entry, 'delete'):
                    entry.delete(0, 'end')
                elif hasattr(entry, 'set'):
                    entry.set('')
                elif hasattr(entry, 'set_date') and CALENDAR_AVAILABLE:
                    entry.set_date(datetime.now().date())
            
            self.fuel_notes.delete('1.0', 'end')
            self.update_status("Yakıt formu temizlendi")
        except Exception as e:
            print(f"Yakıt form temizleme hatası: {e}")
    
    def update_vehicle(self):
        """Araç güncelle"""
        try:
            plate = self.vehicle_entries['plate'].get().upper().strip()
            if not plate:
                messagebox.showerror("Hata", "Plaka giriniz!")
                return
            
            self.cursor.execute('SELECT * FROM vehicles WHERE plate = ?', (plate,))
            if not self.cursor.fetchone():
                messagebox.showerror("Hata", "Araç bulunamadı!")
                return
            
            purchase_date = self.get_date_value('purchase_date')
            insurance_date = self.get_date_value('insurance_date')
            inspection_date = self.get_date_value('inspection_date')
            
            self.cursor.execute('''
                UPDATE vehicles SET 
                model_year=?, engine_type=?, fuel_type=?, color=?, 
                purchase_date=?, current_km=?, insurance_date=?, inspection_date=?,
                notes=?, updated_date=?
                WHERE plate=?
            ''', (
                self.vehicle_entries['model_year'].get(),
                self.vehicle_entries['engine_type'].get(),
                self.vehicle_entries['fuel_type'].get(),
                self.vehicle_entries['color'].get(),
                purchase_date,
                self.vehicle_entries['current_km'].get(),
                insurance_date,
                inspection_date,
                self.vehicle_notes.get('1.0', 'end-1c'),
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                plate
            ))
            
            self.conn.commit()
            messagebox.showinfo("Başarılı", "Araç güncellendi!")
            self.load_vehicle_data()
            self.update_status(f"Araç güncellendi: {plate}")
            
        except Exception as e:
            messagebox.showerror("Hata", f"Güncelleme hatası: {str(e)}")
    
    def delete_vehicle(self):
        """Araç sil"""
        plate = self.vehicle_entries['plate'].get().upper().strip()
        if not plate:
            messagebox.showerror("Hata", "Plaka giriniz!")
            return
        
        response = messagebox.askyesno("Onay", 
            f"{plate} plakalı araç ve tüm kayıtları silinsin mi?\n\nBu işlem geri alınamaz!")
        
        if response:
            try:
                self.cursor.execute('DELETE FROM reminders WHERE vehicle_plate = ?', (plate,))
                self.cursor.execute('DELETE FROM fuel_records WHERE vehicle_plate = ?', (plate,))
                self.cursor.execute('DELETE FROM maintenance WHERE vehicle_plate = ?', (plate,))
                self.cursor.execute('DELETE FROM vehicles WHERE plate = ?', (plate,))
                
                self.conn.commit()
                messagebox.showinfo("Başarılı", "Araç silindi!")
                self.clear_vehicle_form()
                self.load_vehicle_data()
                self.update_status(f"Araç silindi: {plate}")
                
            except Exception as e:
                messagebox.showerror("Hata", f"Silme hatası: {str(e)}")
    
    def get_dashboard_stats(self):
        """Dashboard istatistikleri"""
        stats = {}
        
        # Araç sayısı
        self.cursor.execute('SELECT COUNT(*) FROM vehicles')
        stats['vehicles'] = self.cursor.fetchone()[0]
        
        # Bu ay bakım
        current_month = datetime.now().strftime('%Y-%m')
        self.cursor.execute('SELECT COUNT(*) FROM maintenance WHERE service_date LIKE ?', (f'{current_month}%',))
        stats['maintenance'] = self.cursor.fetchone()[0]
        
        # Toplam maliyet
        self.cursor.execute('SELECT SUM(cost) FROM maintenance WHERE cost IS NOT NULL')
        maintenance_cost = self.cursor.fetchone()[0] or 0
        
        self.cursor.execute('SELECT SUM(total_cost) FROM fuel_records WHERE total_cost IS NOT NULL')
        fuel_cost = self.cursor.fetchone()[0] or 0
        
        stats['cost'] = maintenance_cost + fuel_cost
        
        # Uyarılar (yaklaşan muayene/sigorta)
        today = datetime.now().date()
        alert_count = 0
        
        self.cursor.execute('SELECT insurance_date, inspection_date FROM vehicles')
        for insurance_date, inspection_date in self.cursor.fetchall():
            try:
                if insurance_date:
                    ins_date = datetime.strptime(insurance_date, '%Y-%m-%d').date()
                    if (ins_date - today).days <= 30 and (ins_date - today).days >= 0:
                        alert_count += 1
                
                if inspection_date:
                    insp_date = datetime.strptime(inspection_date, '%Y-%m-%d').date()
                    if (insp_date - today).days <= 30 and (insp_date - today).days >= 0:
                        alert_count += 1
            except:
                continue
        
        stats['alerts'] = alert_count
        
        return stats
    
    def get_analytics_stats(self):
        """Analitik istatistikleri"""
        stats = {}
        
        # Ortalama tüketim
        # Yakıt kayıtlarından en düşük ve en yüksek KM'yi alarak toplam mesafeyi bul
        self.cursor.execute('SELECT vehicle_plate, MIN(km_at_fuel), MAX(km_at_fuel), SUM(liters) FROM fuel_records GROUP BY vehicle_plate')
        fuel_data = self.cursor.fetchall()
        
        total_liters_for_consumption = 0
        total_distance_for_consumption = 0
        
        for plate, min_km, max_km, liters in fuel_data:
            if min_km is not None and max_km is not None and liters is not None:
                distance = max_km - min_km
                if distance > 0:
                    total_liters_for_consumption += liters
                    total_distance_for_consumption += distance
        
        stats['avg_consumption'] = (total_liters_for_consumption / total_distance_for_consumption) * 100 if total_distance_for_consumption > 0 else 0
        
        # Aylık ortalama maliyet (son 12 ay)
        monthly_costs = []
        for i in range(12):
            date = datetime.now() - timedelta(days=30*i)
            month_str = date.strftime('%Y-%m')
            
            self.cursor.execute('SELECT SUM(cost) FROM maintenance WHERE service_date LIKE ? AND cost IS NOT NULL', (f'{month_str}%',))
            maintenance_cost = self.cursor.fetchone()[0] or 0
            
            self.cursor.execute('SELECT SUM(total_cost) FROM fuel_records WHERE fuel_date LIKE ? AND total_cost IS NOT NULL', (f'{month_str}%',))
            fuel_cost = self.cursor.fetchone()[0] or 0
            
            monthly_costs.append(maintenance_cost + fuel_cost)
        
        stats['monthly_avg'] = sum(monthly_costs) / len(monthly_costs) if monthly_costs else 0
        
        # En pahalı bakım
        self.cursor.execute('SELECT MAX(cost) FROM maintenance WHERE cost IS NOT NULL')
        stats['max_maintenance'] = self.cursor.fetchone()[0] or 0
        
        # Toplam mesafe (kayıtlı araçların en yüksek KM'si)
        self.cursor.execute('SELECT MAX(current_km) FROM vehicles')
        stats['total_distance'] = self.cursor.fetchone()[0] or 0
        
        return stats
    
    def create_expense_chart(self, parent):
        """Gider grafiği oluştur"""
        if not MATPLOTLIB_AVAILABLE:
            no_chart = tk.Label(parent,
                               text="📊\nGrafik kütüphanesi mevcut değil\nMatplotlib yükleyin",
                               font=self.fonts['body'],
                               bg=self.colors['bg_secondary'],
                               fg=self.colors['text_secondary'],
                               justify='center')
            no_chart.pack(expand=True)
            return
        
        # Son 6 ayın verilerini al
        months = []
        costs = []
        
        for i in range(6):
            date = datetime.now() - timedelta(days=30*i)
            month_str = date.strftime('%Y-%m')
            months.append(date.strftime('%m/%Y'))
            
            # O ayın toplam maliyetini hesapla
            self.cursor.execute('''
                SELECT SUM(cost) FROM maintenance 
                WHERE service_date LIKE ? AND cost IS NOT NULL
            ''', (f'{month_str}%',))
            
            maintenance_cost = self.cursor.fetchone()[0] or 0
            
            self.cursor.execute('''
                SELECT SUM(total_cost) FROM fuel_records 
                WHERE fuel_date LIKE ? AND total_cost IS NOT NULL
            ''', (f'{month_str}%',))
            
            fuel_cost = self.cursor.fetchone()[0] or 0
            
            costs.append(maintenance_cost + fuel_cost)
        
        months.reverse()
        costs.reverse()
        
        # Grafik oluştur
        fig, ax = plt.subplots(figsize=(8, 4))
        ax.plot(months, costs, marker='o', linewidth=3, markersize=8, color=self.colors['accent_purple'])
        ax.fill_between(months, costs, alpha=0.3, color=self.colors['accent_purple'])
        
        ax.set_title('Aylık Giderler', fontsize=14, fontweight='bold')
        ax.set_ylabel('Maliyet (₺)')
        ax.grid(True, alpha=0.3)
        
        # Tema ayarları
        if self.current_theme == 'koyu':
            fig.patch.set_facecolor(self.colors['bg_secondary'])
            ax.set_facecolor(self.colors['bg_secondary'])
            ax.tick_params(colors=self.colors['text_primary'])
            ax.xaxis.label.set_color(self.colors['text_primary'])
            ax.yaxis.label.set_color(self.colors['text_primary'])
            ax.title.set_color(self.colors['text_primary'])
            for spine in ax.spines.values():
                spine.set_color(self.colors['border'])
        else: # Açık tema için
            fig.patch.set_facecolor(self.colors['bg_secondary'])
            ax.set_facecolor(self.colors['bg_secondary'])
            ax.tick_params(colors=self.colors['text_primary'])
            ax.xaxis.label.set_color(self.colors['text_primary'])
            ax.yaxis.label.set_color(self.colors['text_primary'])
            ax.title.set_color(self.colors['text_primary'])
            for spine in ax.spines.values():
                spine.set_color(self.colors['border'])

        plt.xticks(rotation=45)
        plt.tight_layout()
        
        # Tkinter'a entegre et
        canvas = FigureCanvasTkAgg(fig, parent)
        canvas.draw()
        canvas.get_tk_widget().pack(fill='both', expand=True, padx=10, pady=10)
    
    def create_fuel_consumption_chart(self, parent):
        """Yakıt tüketim grafiği"""
        if not MATPLOTLIB_AVAILABLE:
            no_chart = tk.Label(parent,
                               text="📊\nGrafik kütüphanesi mevcut değil",
                               font=self.fonts['body'],
                               bg=self.colors['bg_secondary'],
                               fg=self.colors['text_secondary'],
                               justify='center')
            no_chart.pack(expand=True)
            return
        
        # Yakıt tüketim verilerini al
        self.cursor.execute('''
            SELECT fuel_date, liters FROM fuel_records 
            WHERE liters > 0 
            ORDER BY fuel_date DESC LIMIT 10
        ''')
        
        data = self.cursor.fetchall()
        if not data:
            no_data = tk.Label(parent,
                              text="Yakıt verisi bulunamadı",
                              font=self.fonts['body'],
                              bg=self.colors['bg_secondary'],
                              fg=self.colors['text_secondary'])
            no_data.pack(expand=True)
            return
        
        dates = [row[0] for row in data]
        liters = [row[1] for row in data]
        
        dates.reverse()
        liters.reverse()
        
        # Grafik oluştur
        fig, ax = plt.subplots(figsize=(8, 4))
        ax.bar(range(len(dates)), liters, color=self.colors['accent_red'], alpha=0.7)
        
        ax.set_title('Yakıt Tüketimi', fontsize=14, fontweight='bold')
        ax.set_ylabel('Litre')
        ax.set_xticks(range(len(dates)))
        ax.set_xticklabels([d[:10] for d in dates], rotation=45)
        
        # Tema ayarları
        if self.current_theme == 'koyu':
            fig.patch.set_facecolor(self.colors['bg_secondary'])
            ax.set_facecolor(self.colors['bg_secondary'])
            ax.tick_params(colors=self.colors['text_primary'])
            ax.xaxis.label.set_color(self.colors['text_primary'])
            ax.yaxis.label.set_color(self.colors['text_primary'])
            ax.title.set_color(self.colors['text_primary'])
            for spine in ax.spines.values():
                spine.set_color(self.colors['border'])
        else: # Açık tema için
            fig.patch.set_facecolor(self.colors['bg_secondary'])
            ax.set_facecolor(self.colors['bg_secondary'])
            ax.tick_params(colors=self.colors['text_primary'])
            ax.xaxis.label.set_color(self.colors['text_primary'])
            ax.yaxis.label.set_color(self.colors['text_primary'])
            ax.title.set_color(self.colors['text_primary'])
            for spine in ax.spines.values():
                spine.set_color(self.colors['border'])
        
        plt.tight_layout()
        
        canvas = FigureCanvasTkAgg(fig, parent)
        canvas.draw()
        canvas.get_tk_widget().pack(fill='both', expand=True, padx=10, pady=10)
    
    def create_cost_distribution_chart(self, parent):
        """Maliyet dağılım grafiği"""
        if not MATPLOTLIB_AVAILABLE:
            no_chart = tk.Label(parent,
                               text="📊\nGrafik kütüphanesi mevcut değil",
                               font=self.fonts['body'],
                               bg=self.colors['bg_secondary'],
                               fg=self.colors['text_secondary'],
                               justify='center')
            no_chart.pack(expand=True)
            return
        
        # Maliyet dağılımını al
        self.cursor.execute('SELECT SUM(cost) FROM maintenance WHERE cost IS NOT NULL')
        maintenance_total = self.cursor.fetchone()[0] or 0
        
        self.cursor.execute('SELECT SUM(total_cost) FROM fuel_records WHERE total_cost IS NOT NULL')
        fuel_total = self.cursor.fetchone()[0] or 0
        
        if maintenance_total == 0 and fuel_total == 0:
            no_data = tk.Label(parent,
                              text="Maliyet verisi bulunamadı",
                              font=self.fonts['body'],
                              bg=self.colors['bg_secondary'],
                              fg=self.colors['text_secondary'])
            no_data.pack(expand=True)
            return
        
        # Pasta grafiği
        labels = ['Bakım', 'Yakıt']
        sizes = [maintenance_total, fuel_total]
        colors = [self.colors['accent_orange'], self.colors['accent_red']]
        
        fig, ax = plt.subplots(figsize=(6, 6))
        ax.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
        ax.set_title('Maliyet Dağılımı', fontsize=14, fontweight='bold')
        
        # Tema ayarları
        if self.current_theme == 'koyu':
            fig.patch.set_facecolor(self.colors['bg_secondary'])
            ax.title.set_color(self.colors['text_primary'])
        else: # Açık tema için
            fig.patch.set_facecolor(self.colors['bg_secondary'])
            ax.title.set_color(self.colors['text_primary'])
        
        plt.tight_layout()
        
        canvas = FigureCanvasTkAgg(fig, parent)
        canvas.draw()
        canvas.get_tk_widget().pack(fill='both', expand=True, padx=10, pady=10)
    
    def create_activity_list(self, parent):
        """Aktivite listesi oluştur"""
        # Son aktiviteleri al
        self.cursor.execute('''
            SELECT 'Bakım' as type, service_date as date, vehicle_plate, service_type as detail
            FROM maintenance 
            UNION ALL
            SELECT 'Yakıt' as type, fuel_date as date, vehicle_plate, 
                   CAST(liters AS TEXT) || ' L' as detail
            FROM fuel_records
            ORDER BY date DESC LIMIT 8
        ''')
        
        activities = self.cursor.fetchall()
        
        if not activities:
            no_activity = tk.Label(parent,
                                  text="Henüz aktivite yok",
                                  font=self.fonts['body'],
                                  bg=self.colors['bg_secondary'],
                                  fg=self.colors['text_secondary'])
            no_activity.pack(expand=True)
            return
        
        for activity_type, date, plate, detail in activities:
            activity_item = tk.Frame(parent, bg=self.colors['bg_tertiary'])
            activity_item.pack(fill='x', pady=5, padx=10)
            
            # İkon
            icon = "🔧" if activity_type == "Bakım" else "⛽"
            icon_label = tk.Label(activity_item,
                                 text=icon,
                                 font=('Segoe UI', 16),
                                 bg=self.colors['bg_tertiary'],
                                 fg=self.colors['accent_blue'])
            icon_label.pack(side='left', padx=15, pady=10)
            
            # Bilgi
            info_frame = tk.Frame(activity_item, bg=self.colors['bg_tertiary'])
            info_frame.pack(side='left', fill='x', expand=True, pady=10)
            
            action_label = tk.Label(info_frame,
                                   text=f"{activity_type} - {detail}",
                                   font=self.fonts['body'],
                                   bg=self.colors['bg_tertiary'],
                                   fg=self.colors['text_primary'])
            action_label.pack(anchor='w')
            
            detail_label = tk.Label(info_frame,
                                   text=f"{plate} • {date}",
                                   font=self.fonts['small'],
                                   bg=self.colors['bg_tertiary'],
                                   fg=self.colors['text_secondary'])
            detail_label.pack(anchor='w')
    
    def create_active_reminders(self, parent):
        """Aktif hatırlatmalar"""
        # Aktif hatırlatmaları al
        today = datetime.now().date()
        
        reminders = []
        
        # Sigorta hatırlatmaları
        self.cursor.execute('SELECT plate, insurance_date FROM vehicles WHERE insurance_date IS NOT NULL')
        for plate, insurance_date in self.cursor.fetchall():
            try:
                ins_date = datetime.strptime(insurance_date, '%Y-%m-%d').date()
                days_left = (ins_date - today).days
                if days_left <= 30 and days_left >= 0: # Sadece gelecek 30 gün ve geçmişe düşmemiş olanlar
                    status = "Acil" if days_left <= 7 else "Yaklaşan"
                    reminders.append((f"🛡️ {plate} Sigorta", f"{days_left} gün kaldı", status))
            except ValueError:
                # Tarih formatı hatası
                pass
        
        # Muayene hatırlatmaları
        self.cursor.execute('SELECT plate, inspection_date FROM vehicles WHERE inspection_date IS NOT NULL')
        for plate, inspection_date in self.cursor.fetchall():
            try:
                insp_date = datetime.strptime(inspection_date, '%Y-%m-%d').date()
                days_left = (insp_date - today).days
                if days_left <= 30 and days_left >= 0: # Sadece gelecek 30 gün ve geçmişe düşmemiş olanlar
                    status = "Acil" if days_left <= 7 else "Yaklaşan"
                    reminders.append((f"🔍 {plate} Muayene", f"{days_left} gün kaldı", status))
            except ValueError:
                # Tarih formatı hatası
                pass
        
        # Özel hatırlatmalar (reminders tablosundan)
        self.cursor.execute('SELECT title, description, due_date, due_km, priority, status FROM reminders WHERE status = "Aktif"')
        for title, description, due_date_str, due_km, priority, status in self.cursor.fetchall():
            try:
                if due_date_str:
                    due_date = datetime.strptime(due_date_str, '%Y-%m-%d').date()
                    days_left = (due_date - today).days
                    if days_left <= 30 and days_left >= 0:
                        status_text = "Acil" if days_left <= 7 else "Yaklaşan"
                        reminders.append((f"🔔 {title}", f"{days_left} gün kaldı - {description}", status_text))
                # KM bazlı hatırlatmalar için ek kontrol eklenebilir
            except ValueError:
                pass

        if not reminders:
            no_reminder = tk.Label(parent,
                                  text="Aktif hatırlatma yok",
                                  font=self.fonts['body'],
                                  bg=self.colors['bg_secondary'],
                                  fg=self.colors['text_secondary'])
            no_reminder.pack(expand=True)
            return
        
        for title, desc, status in reminders:
            reminder_item = tk.Frame(parent, bg=self.colors['bg_tertiary'])
            reminder_item.pack(fill='x', pady=5, padx=10)
            
            # Durum rengi
            status_color = self.colors['accent_red'] if status == "Acil" else self.colors['accent_orange']
            
            status_label = tk.Label(reminder_item,
                                   text=status,
                                   font=self.fonts['small'],
                                   bg=status_color,
                                   fg='white',
                                   padx=10, pady=2)
            status_label.pack(side='right', padx=10, pady=10)
            
            # Bilgi
            info_frame = tk.Frame(reminder_item, bg=self.colors['bg_tertiary'])
            info_frame.pack(side='left', fill='x', expand=True, pady=10, padx=10)
            
            title_label = tk.Label(info_frame,
                                  text=title,
                                  font=self.fonts['body'],
                                  bg=self.colors['bg_tertiary'],
                                  fg=self.colors['text_primary'])
            title_label.pack(anchor='w')
            
            desc_label = tk.Label(info_frame,
                                 text=desc,
                                 font=self.fonts['small'],
                                 bg=self.colors['bg_tertiary'],
                                 fg=self.colors['text_secondary'])
            desc_label.pack(anchor='w')
    
    def create_upcoming_reminders(self, parent):
        """Yaklaşan hatırlatmalar"""
        # Yaklaşan hatırlatmaları al (örneğin, 30 günden sonraki ama 90 gün içindeki)
        today = datetime.now().date()
        upcoming_reminders = []

        # Sigorta hatırlatmaları
        self.cursor.execute('SELECT plate, insurance_date FROM vehicles WHERE insurance_date IS NOT NULL')
        for plate, insurance_date in self.cursor.fetchall():
            try:
                ins_date = datetime.strptime(insurance_date, '%Y-%m-%d').date()
                days_left = (ins_date - today).days
                if 30 < days_left <= 90:
                    upcoming_reminders.append((f"🛡️ {plate} Sigorta", f"{days_left} gün kaldı", "Planlı"))
            except ValueError:
                pass

        # Muayene hatırlatmaları
        self.cursor.execute('SELECT plate, inspection_date FROM vehicles WHERE inspection_date IS NOT NULL')
        for plate, inspection_date in self.cursor.fetchall():
            try:
                insp_date = datetime.strptime(inspection_date, '%Y-%m-%d').date()
                days_left = (insp_date - today).days
                if 30 < days_left <= 90:
                    upcoming_reminders.append((f"🔍 {plate} Muayene", f"{days_left} gün kaldı", "Planlı"))
            except ValueError:
                pass
        
        # Özel hatırlatmalar (reminders tablosundan)
        self.cursor.execute('SELECT title, description, due_date, due_km, priority, status FROM reminders WHERE status = "Aktif"')
        for title, description, due_date_str, due_km, priority, status in self.cursor.fetchall():
            try:
                if due_date_str:
                    due_date = datetime.strptime(due_date_str, '%Y-%m-%d').date()
                    days_left = (due_date - today).days
                    if 30 < days_left <= 90:
                        upcoming_reminders.append((f"🔔 {title}", f"{days_left} gün kaldı - {description}", "Planlı"))
            except ValueError:
                pass

        if not upcoming_reminders:
            no_reminder = tk.Label(parent,
                                  text="Yaklaşan hatırlatma yok",
                                  font=self.fonts['body'],
                                  bg=self.colors['bg_secondary'],
                                  fg=self.colors['text_secondary'])
            no_reminder.pack(expand=True)
            return
        
        for title, desc, status in upcoming_reminders:
            reminder_item = tk.Frame(parent, bg=self.colors['bg_tertiary'])
            reminder_item.pack(fill='x', pady=5, padx=10)
            
            status_label = tk.Label(reminder_item,
                                   text=status,
                                   font=self.fonts['small'],
                                   bg=self.colors['accent_blue'], # Planlı için farklı renk
                                   fg='white',
                                   padx=10, pady=2)
            status_label.pack(side='right', padx=10, pady=10)
            
            info_frame = tk.Frame(reminder_item, bg=self.colors['bg_tertiary'])
            info_frame.pack(side='left', fill='x', expand=True, pady=10, padx=10)
            
            title_label = tk.Label(info_frame,
                                  text=title,
                                  font=self.fonts['body'],
                                  bg=self.colors['bg_tertiary'],
                                  fg=self.colors['text_primary'])
            title_label.pack(anchor='w')
            
            desc_label = tk.Label(info_frame,
                                 text=desc,
                                 font=self.fonts['small'],
                                 bg=self.colors['bg_tertiary'],
                                 fg=self.colors['text_secondary'])
            desc_label.pack(anchor='w')
    
    def create_general_settings(self, parent):
        """Genel ayarlar"""
        settings_card, settings_content = self.create_card(parent, "Genel Ayarlar", 
                                                          color=self.colors['accent_blue'])
        settings_card.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Tema ayarı
        theme_frame = tk.Frame(settings_content, bg=self.colors['bg_secondary'])
        theme_frame.pack(fill='x', pady=10)
        
        tk.Label(theme_frame, text="Tema:", font=self.fonts['body'],
                bg=self.colors['bg_secondary'], fg=self.colors['text_primary']).pack(side='left')
        
        theme_btn = self.create_button(theme_frame, 
                                      "🌙 Koyu Tema" if self.current_theme == 'koyu' else "☀️ Açık Tema",
                                      self.toggle_theme, self.colors['accent_purple'])
        theme_btn.pack(side='right')
        
        # Dil ayarı
        lang_frame = tk.Frame(settings_content, bg=self.colors['bg_secondary'])
        lang_frame.pack(fill='x', pady=10)
        
        tk.Label(lang_frame, text="Dil:", font=self.fonts['body'],
                bg=self.colors['bg_secondary'], fg=self.colors['text_primary']).pack(side='left')
        
        lang_combo = ttk.Combobox(lang_frame, values=["Türkçe", "English"], state="readonly")
        lang_combo.set("Türkçe")
        lang_combo.pack(side='right')
        
        # Otomatik yedekleme
        backup_frame = tk.Frame(settings_content, bg=self.colors['bg_secondary'])
        backup_frame.pack(fill='x', pady=10)
        
        tk.Label(backup_frame, text="Otomatik Yedekleme:", font=self.fonts['body'],
                bg=self.colors['bg_secondary'], fg=self.colors['text_primary']).pack(side='left')
        
        self.auto_backup_var = tk.BooleanVar(value=self.get_setting('auto_backup', 'True') == 'True')
        backup_check = tk.Checkbutton(backup_frame, variable=self.auto_backup_var,
                                     bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                                     command=self.save_auto_backup_setting)
        backup_check.pack(side='right')
    
    def create_backup_settings(self, parent):
        """Yedekleme ayarları"""
        backup_card, backup_content = self.create_card(parent, "Veri Yedekleme", 
                                                      color=self.colors['accent_green'])
        backup_card.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Yedekleme butonları
        button_frame = tk.Frame(backup_content, bg=self.colors['bg_secondary'])
        button_frame.pack(fill='x', pady=20)
        
        backup_btn = self.create_button(button_frame, "💾 Yedek Oluştur", 
                                       self.backup_data, self.colors['accent_green'])
        backup_btn.pack(side='left', padx=5)
        
        restore_btn = self.create_button(button_frame, "📂 Yedekten Geri Yükle", 
                                        self.restore_data, self.colors['accent_orange'])
        restore_btn.pack(side='left', padx=5)
        
        # Yedek bilgileri
        info_text = """
Yedekleme Bilgileri:

• Otomatik yedekleme her gün çalışır
• Yedek dosyaları JSON formatında saklanır
• Tüm araç, bakım ve yakıt verileri yedeklenir
• Yedek dosyalarını güvenli yerde saklayın
        """
        
        info_label = tk.Label(backup_content, text=info_text,
                             font=self.fonts['body'],
                             bg=self.colors['bg_secondary'],
                             fg=self.colors['text_secondary'],
                             justify='left')
        info_label.pack(pady=20)
    
    def create_about_section(self, parent):
        """Hakkında bölümü"""
        about_card, about_content = self.create_card(parent, "Hakkında", 
                                                    color=self.colors['accent_purple'])
        about_card.pack(fill='both', expand=True, padx=20, pady=20)
        
        about_text = """
🚗 FIAT STILO PRO - Türkçe Araç Takip Sistemi

Sürüm: 3.0 Pro
Geliştirici: Python Otomasyon Sistemi
Tarih: 2024

ÖZELLİKLER:
✅ Modern ve kullanıcı dostu arayüz
✅ Koyu/Açık tema desteği
✅ Araç bilgileri yönetimi
✅ Bakım kayıtları takibi
✅ Yakıt tüketimi analizi
✅ Gelişmiş raporlama sistemi
✅ Akıllı hatırlatmalar
✅ Grafik ve analitik
✅ Otomatik yedekleme
✅ Veri dışa aktarma

TEKNIK BİLGİLER:
• Python 3.7+ gerekli
• SQLite veritabanı
• Tkinter GUI framework
• Matplotlib grafik desteği
• PIL resim işleme
• tkcalendar tarih seçici

DESTEK:
Bu program açık kaynak kodlu olarak geliştirilmiştir.
Tüm verileriniz yerel olarak saklanır.
        """
        
        about_label = tk.Label(about_content, text=about_text,
                              font=self.fonts['small'],
                              bg=self.colors['bg_secondary'],
                              fg=self.colors['text_primary'],
                              justify='left')
        about_label.pack(pady=10)
    
    # Rapor metodları
    def generate_maintenance_report(self):
        """Bakım raporu oluştur"""
        self.report_text.delete('1.0', 'end')
        
        # Toplam bakım sayısı
        self.cursor.execute('SELECT COUNT(*) FROM maintenance')
        total_maintenance = self.cursor.fetchone()[0]
        
        # Bakım türlerine göre dağılım
        self.cursor.execute('SELECT service_type, COUNT(*) FROM maintenance GROUP BY service_type')
        service_types = self.cursor.fetchall()
        
        # Toplam maliyet
        self.cursor.execute('SELECT SUM(cost) FROM maintenance WHERE cost IS NOT NULL')
        total_cost = self.cursor.fetchone()[0] or 0
        
        report = f"📊 BAKIM RAPORU\n"
        report += "=" * 50 + "\n\n"
        report += f"Toplam Bakım Sayısı: {total_maintenance}\n"
        report += f"Toplam Bakım Maliyeti: {total_cost:,.2f} ₺\n\n"
        
        if service_types:
            report += "Bakım Türleri Dağılımı:\n"
            report += "-" * 30 + "\n"
            for service_type, count in service_types:
                report += f"{service_type}: {count} adet\n"
        
        self.report_text.insert('1.0', report)
        self.update_status("Bakım raporu oluşturuldu")
    
    def generate_fuel_report(self):
        """Yakıt raporu oluştur"""
        self.report_text.delete('1.0', 'end')
        
        # Toplam yakıt ve maliyet
        self.cursor.execute('SELECT SUM(liters), SUM(total_cost) FROM fuel_records')
        result = self.cursor.fetchone()
        total_liters = result[0] or 0
        total_fuel_cost = result[1] or 0
        
        # Ortalama tüketim hesaplama
        self.cursor.execute('''
            SELECT vehicle_plate, MIN(km_at_fuel), MAX(km_at_fuel), SUM(liters)
            FROM fuel_records 
            GROUP BY vehicle_plate
        ''')
        consumption_data = self.cursor.fetchall()
        
        report = f"⛽ YAKIT RAPORU\n"
        report += "=" * 50 + "\n\n"
        report += f"Toplam Yakıt: {total_liters:.2f} litre\n"
        report += f"Toplam Yakıt Maliyeti: {total_fuel_cost:,.2f} ₺\n\n"
        
        if consumption_data:
            report += "Araç Bazında Tüketim:\n"
            report += "-" * 30 + "\n"
            for plate, min_km, max_km, liters in consumption_data:
                if max_km is not None and min_km is not None and liters is not None:
                    distance = max_km - min_km
                    consumption = (liters / distance) * 100 if distance > 0 else 0
                    report += f"{plate}: {consumption:.2f} L/100km\n"
        
        self.report_text.insert('1.0', report)
        self.update_status("Yakıt raporu oluşturuldu")
    
    def generate_cost_report(self):
        """Maliyet raporu oluştur"""
        self.report_text.delete('1.0', 'end')
        
        # Bakım maliyeti
        self.cursor.execute('SELECT SUM(cost) FROM maintenance WHERE cost IS NOT NULL')
        maintenance_cost = self.cursor.fetchone()[0] or 0
        
        # Yakıt maliyeti
        self.cursor.execute('SELECT SUM(total_cost) FROM fuel_records WHERE total_cost IS NOT NULL')
        fuel_cost = self.cursor.fetchone()[0] or 0
        
        total_cost = maintenance_cost + fuel_cost
        
        report = f"💰 MALİYET ANALİZİ\n"
        report += "=" * 50 + "\n\n"
        report += f"Bakım Maliyeti: {maintenance_cost:,.2f} ₺\n"
        report += f"Yakıt Maliyeti: {fuel_cost:,.2f} ₺\n"
        report += f"Toplam Maliyet: {total_cost:,.2f} ₺\n\n"
        
        if total_cost > 0:
            maintenance_percent = (maintenance_cost / total_cost) * 100
            fuel_percent = (fuel_cost / total_cost) * 100
            
            report += "Maliyet Dağılımı:\n"
            report += "-" * 20 + "\n"
            report += f"Bakım: %{maintenance_percent:.1f}\n"
            report += f"Yakıt: %{fuel_percent:.1f}\n"
        
        self.report_text.insert('1.0', report)
        self.update_status("Maliyet raporu oluşturuldu")
    
    def generate_general_report(self):
        """Genel rapor oluştur"""
        self.report_text.delete('1.0', 'end')
        
        # Genel istatistikler
        self.cursor.execute('SELECT COUNT(*) FROM vehicles')
        vehicle_count = self.cursor.fetchone()[0]
        
        self.cursor.execute('SELECT COUNT(*) FROM maintenance')
        maintenance_count = self.cursor.fetchone()[0]
        
        self.cursor.execute('SELECT COUNT(*) FROM fuel_records')
        fuel_count = self.cursor.fetchone()[0]
        
        report = f"📋 GENEL RAPOR\n"
        report += "=" * 50 + "\n\n"
        report += f"Kayıtlı Araç Sayısı: {vehicle_count}\n"
        report += f"Toplam Bakım Kaydı: {maintenance_count}\n"
        report += f"Toplam Yakıt Kaydı: {fuel_count}\n\n"
        
        # Son aktiviteler
        self.cursor.execute('''
            SELECT 'Bakım' as type, service_date as date, vehicle_plate 
            FROM maintenance 
            UNION ALL
            SELECT 'Yakıt' as type, fuel_date as date, vehicle_plate 
            FROM fuel_records
            ORDER BY date DESC LIMIT 5
        ''')
        
        recent_activities = self.cursor.fetchall()
        
        if recent_activities:
            report += "Son Aktiviteler:\n"
            report += "-" * 20 + "\n"
            for activity_type, date, plate in recent_activities:
                report += f"{date} - {plate} - {activity_type}\n"
        
        self.report_text.insert('1.0', report)
        self.update_status("Genel rapor oluşturuldu")
    
    def export_to_excel(self):
        """Excel'e aktar"""
        try:
            import openpyxl
            from openpyxl import Workbook
            
            wb = Workbook()
            
            # Araçlar sayfası
            ws1 = wb.active
            ws1.title = "Araçlar"
            ws1.append(["Plaka", "Model Yılı", "Motor", "Yakıt", "Renk", "Mevcut KM", "Alış Tarihi", "Sigorta Tarihi", "Muayene Tarihi", "Notlar"])
            
            self.cursor.execute('SELECT plate, model_year, engine_type, fuel_type, color, current_km, purchase_date, insurance_date, inspection_date, notes FROM vehicles')
            for row in self.cursor.fetchall():
                ws1.append(row)
            
            # Bakım sayfası
            ws2 = wb.create_sheet("Bakım")
            ws2.append(["Plaka", "Bakım Tipi", "Tarih", "KM", "Maliyet", "Servis Yeri", "Değiştirilen Parçalar", "Notlar"])
            
            self.cursor.execute('SELECT vehicle_plate, service_type, service_date, km_at_service, cost, service_location, parts_changed, notes FROM maintenance')
            for row in self.cursor.fetchall():
                ws2.append(row)
            
            # Yakıt sayfası
            ws3 = wb.create_sheet("Yakıt")
            ws3.append(["Plaka", "Tarih", "KM", "Litre", "Litre Fiyatı", "Toplam", "İstasyon", "Yakıt Tipi", "Notlar"])
            
            self.cursor.execute('SELECT vehicle_plate, fuel_date, km_at_fuel, liters, cost_per_liter, total_cost, fuel_station, fuel_type, notes FROM fuel_records')
            for row in self.cursor.fetchall():
                ws3.append(row)
            
            # Dosyayı kaydet
            filename = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                title="Excel Dosyası Kaydet"
            )
            
            if filename:
                wb.save(filename)
                messagebox.showinfo("Başarılı", f"Veriler Excel'e aktarıldı:\n{filename}")
                self.update_status("Excel'e aktarım tamamlandı")
            
        except ImportError:
            messagebox.showerror("Hata", "openpyxl kütüphanesi gerekli!\npip install openpyxl")
        except Exception as e:
            messagebox.showerror("Hata", f"Excel aktarım hatası: {str(e)}")
    
    def export_to_pdf(self):
        """PDF oluştur"""
        try:
            from reportlab.pdfgen import canvas
            from reportlab.lib.pagesizes import letter
            from reportlab.lib.units import inch
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
            from reportlab.lib.styles import getSampleStyleSheet
            from reportlab.lib import colors
            
            filename = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
                title="PDF Dosyası Kaydet"
            )
            
            if filename:
                doc = SimpleDocTemplate(filename, pagesize=letter)
                styles = getSampleStyleSheet()
                story = []

                # Başlık
                story.append(Paragraph("Fiat Stilo Araç Raporu", styles['h1']))
                story.append(Spacer(1, 0.2 * inch))
                story.append(Paragraph(f"Tarih: {datetime.now().strftime('%d.%m.%Y')}", styles['Normal']))
                story.append(Spacer(1, 0.2 * inch))

                # İstatistikler
                stats = self.get_dashboard_stats()
                story.append(Paragraph("Genel İstatistikler:", styles['h2']))
                story.append(Paragraph(f"Toplam Araç: {stats['vehicles']}", styles['Normal']))
                story.append(Paragraph(f"Bu Ay Bakım: {stats['maintenance']}", styles['Normal']))
                story.append(Paragraph(f"Toplam Maliyet: {stats['cost']:,.0f} TL", styles['Normal']))
                story.append(Spacer(1, 0.2 * inch))

                # Araç Listesi
                story.append(Paragraph("Araç Listesi:", styles['h2']))
                self.cursor.execute('SELECT plate, model_year, engine_type, fuel_type, color, current_km FROM vehicles')
                vehicle_data = [["Plaka", "Model Yılı", "Motor", "Yakıt", "Renk", "KM"]] + list(self.cursor.fetchall())
                if len(vehicle_data) > 1:
                    table = Table(vehicle_data)
                    table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black)
                    ]))
                    story.append(table)
                else:
                    story.append(Paragraph("Kayıtlı araç bulunamadı.", styles['Normal']))
                story.append(Spacer(1, 0.2 * inch))

                # Bakım Kayıtları
                story.append(Paragraph("Bakım Kayıtları:", styles['h2']))
                self.cursor.execute('SELECT vehicle_plate, service_type, service_date, km_at_service, cost FROM maintenance ORDER BY service_date DESC LIMIT 10')
                maintenance_data = [["Plaka", "Bakım Tipi", "Tarih", "KM", "Maliyet"]] + list(self.cursor.fetchall())
                if len(maintenance_data) > 1:
                    table = Table(maintenance_data)
                    table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                        ('BACKGROUND', (0, 1), (-1, -1), colors.lightgrey),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black)
                    ]))
                    story.append(table)
                else:
                    story.append(Paragraph("Kayıtlı bakım bulunamadı.", styles['Normal']))
                story.append(Spacer(1, 0.2 * inch))

                # Yakıt Kayıtları
                story.append(Paragraph("Yakıt Kayıtları:", styles['h2']))
                self.cursor.execute('SELECT vehicle_plate, fuel_date, liters, total_cost FROM fuel_records ORDER BY fuel_date DESC LIMIT 10')
                fuel_data = [["Plaka", "Tarih", "Litre", "Toplam Maliyet"]] + list(self.cursor.fetchall())
                if len(fuel_data) > 1:
                    table = Table(fuel_data)
                    table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black)
                    ]))
                    story.append(table)
                else:
                    story.append("Kayıtlı yakıt bulunamadı.", styles['Normal'])
                story.append(Spacer(1, 0.2 * inch))

                doc.build(story)
                messagebox.showinfo("Başarılı", f"PDF oluşturuldu:\n{filename}")
                self.update_status("PDF oluşturma tamamlandı")
            
        except ImportError:
            messagebox.showerror("Hata", "reportlab kütüphanesi gerekli!\npip install reportlab")
        except Exception as e:
            messagebox.showerror("Hata", f"PDF oluşturma hatası: {str(e)}")
    
    def backup_data(self):
        """Veri yedekleme"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                title="Yedek Dosyası Kaydet"
            )
            
            if filename:
                backup_data = {}
                
                # Tüm verileri al
                tables = ['vehicles', 'maintenance', 'fuel_records', 'reminders', 'settings']
                
                for table in tables:
                    self.cursor.execute(f'SELECT * FROM {table}')
                    backup_data[table] = self.cursor.fetchall()
                
                # JSON dosyasına kaydet
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(backup_data, f, ensure_ascii=False, indent=2)
                
                messagebox.showinfo("Başarılı", f"Veriler yedeklendi:\n{filename}")
                self.update_status("Yedekleme tamamlandı")
        
        except Exception as e:
            messagebox.showerror("Hata", f"Yedekleme hatası: {str(e)}")
    
    def restore_data(self):
        """Veri geri yükleme"""
        try:
            filename = filedialog.askopenfilename(
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                title="Yedek Dosyası Seç"
            )
            
            if filename:
                with open(filename, 'r', encoding='utf-8') as f:
                    backup_data = json.load(f)
                
                response = messagebox.askyesno("Onay", 
                    "Mevcut veriler silinip yedekten geri yüklensin mi?")
                
                if response:
                    # Tabloları temizle
                    tables = ['reminders', 'fuel_records', 'maintenance', 'vehicles', 'settings']
                    for table in tables:
                        self.cursor.execute(f'DELETE FROM {table}')
                    
                    # Yedek verileri geri yükle
                    for table, data in backup_data.items():
                        if table == 'vehicles':
                            for row in data:
                                # ID'yi de ekleyerek insert yap
                                self.cursor.execute('''
                                    INSERT INTO vehicles (id, plate, model_year, engine_type, fuel_type, color,
                                    purchase_date, current_km, insurance_date, inspection_date,
                                    notes, photo_path, created_date, updated_date)
                                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                ''', row)
                        elif table == 'maintenance':
                            for row in data:
                                self.cursor.execute('''
                                    INSERT INTO maintenance (id, vehicle_plate, service_type, service_date, km_at_service,
                                       cost, service_location, parts_changed, next_service_km,
                                       next_service_date, priority, status, notes, created_date)
                                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                ''', row)
                        elif table == 'fuel_records':
                            for row in data:
                                self.cursor.execute('''
                                    INSERT INTO fuel_records (id, vehicle_plate, fuel_date, km_at_fuel, liters,
                                        cost_per_liter, total_cost, fuel_station, fuel_type,
                                        notes, created_date)
                                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                ''', row)
                        elif table == 'reminders':
                            for row in data:
                                self.cursor.execute('''
                                    INSERT INTO reminders (id, vehicle_plate, reminder_type, title, description,
                                         due_date, due_km, priority, status, created_date)
                                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                ''', row)
                        elif table == 'settings':
                            for row in data:
                                self.cursor.execute('''
                                    INSERT INTO settings (id, setting_name, setting_value, updated_date)
                                    VALUES (?, ?, ?, ?)
                                ''', row)
                    
                    self.conn.commit()
                    messagebox.showinfo("Başarılı", "Veriler geri yüklendi!")
                    self.update_status("Geri yükleme tamamlandı")
                    
                    # Arayüzü yenile
                    self.refresh_all_data()
        
        except Exception as e:
            messagebox.showerror("Hata", f"Geri yükleme hatası: {str(e)}")
    
    def start_auto_backup(self):
        """Otomatik yedekleme başlat"""
        def auto_backup_task():
            try:
                if self.auto_backup_var.get():
                    backup_dir = "auto_backups"
                    os.makedirs(backup_dir, exist_ok=True)
                    backup_path = os.path.join(backup_dir, f"auto_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
                    
                    backup_data = {}
                    tables = ['vehicles', 'maintenance', 'fuel_records', 'reminders', 'settings']
                    
                    for table in tables:
                        self.cursor.execute(f'SELECT * FROM {table}')
                        backup_data[table] = self.cursor.fetchall()
                    
                    with open(backup_path, 'w', encoding='utf-8') as f:
                        json.dump(backup_data, f, ensure_ascii=False, indent=2)
                    
                    print(f"Otomatik yedek oluşturuldu: {backup_path}")
            except Exception as e:
                print(f"Otomatik yedekleme hatası: {e}")
            finally:
                # Bir sonraki yedeklemeyi planla
                threading.Timer(86400.0, auto_backup_task).start() # 24 saat = 86400 saniye
        
        # İlk yedeklemeyi başlat
        threading.Timer(5.0, auto_backup_task).start() # Program başladıktan 5 saniye sonra ilk yedek
    
    def add_reminder_dialog(self):
        """Hatırlatma ekleme dialogu"""
        dialog = tk.Toplevel(self.root)
        dialog.title("Yeni Hatırlatma")
        dialog.geometry("450x400")
        dialog.configure(bg=self.colors['bg_primary'])
        dialog.transient(self.root) # Ana pencere üzerinde kalmasını sağlar
        dialog.grab_set() # Ana pencereyi devre dışı bırakır
        
        # Form alanları
        tk.Label(dialog, text="Yeni Hatırlatma Ekle", font=self.fonts['heading'],
                bg=self.colors['bg_primary'], fg=self.colors['text_primary']).pack(pady=20)
        
        # Plaka
        plate_frame = tk.Frame(dialog, bg=self.colors['bg_primary'])
        plate_frame.pack(fill='x', padx=20, pady=5)
        
        tk.Label(plate_frame, text="Plaka:", bg=self.colors['bg_primary'], 
                fg=self.colors['text_primary'], font=self.fonts['body']).pack(side='left', anchor='w')
        
        self.cursor.execute('SELECT DISTINCT plate FROM vehicles')
        plates = [row[0] for row in self.cursor.fetchall()]
        plate_combo = ttk.Combobox(plate_frame, values=plates, font=self.fonts['body'])
        plate_combo.pack(side='right', fill='x', expand=True, padx=(10, 0))
        
        # Başlık
        title_frame = tk.Frame(dialog, bg=self.colors['bg_primary'])
        title_frame.pack(fill='x', padx=20, pady=5)
        
        tk.Label(title_frame, text="Başlık:", bg=self.colors['bg_primary'], 
                fg=self.colors['text_primary'], font=self.fonts['body']).pack(side='left', anchor='w')
        
        title_entry = tk.Entry(title_frame, bg=self.colors['bg_tertiary'], 
                              fg=self.colors['text_primary'], font=self.fonts['body'], relief='flat')
        title_entry.pack(side='right', fill='x', expand=True, padx=(10, 0))
        
        # Açıklama
        desc_frame = tk.Frame(dialog, bg=self.colors['bg_primary'])
        desc_frame.pack(fill='x', padx=20, pady=5)
        
        tk.Label(desc_frame, text="Açıklama:", bg=self.colors['bg_primary'], 
                fg=self.colors['text_primary'], font=self.fonts['body']).pack(anchor='w')
        
        desc_text = tk.Text(desc_frame, height=3, bg=self.colors['bg_tertiary'], 
                           fg=self.colors['text_primary'], font=self.fonts['body'], relief='flat')
        desc_text.pack(fill='x', pady=5)

        # Son Tarih
        date_frame = tk.Frame(dialog, bg=self.colors['bg_primary'])
        date_frame.pack(fill='x', padx=20, pady=5)
        tk.Label(date_frame, text="Son Tarih:", bg=self.colors['bg_primary'], 
                fg=self.colors['text_primary'], font=self.fonts['body']).pack(side='left', anchor='w')
        if CALENDAR_AVAILABLE:
            date_entry = DateEntry(date_frame, background=self.colors['accent_green'],
                                    foreground='white', borderwidth=0, date_pattern='dd.mm.yyyy', font=self.fonts['body'])
            date_entry.set_date(datetime.now().date() + timedelta(days=30)) # Varsayılan 30 gün sonrası
        else:
            date_entry = tk.Entry(date_frame, font=self.fonts['body'],
                                   bg=self.colors['bg_tertiary'], fg=self.colors['text_primary'], relief='flat')
            date_entry.insert(0, (datetime.now() + timedelta(days=30)).strftime('%d.%m.%Y'))
        date_entry.pack(side='right', fill='x', expand=True, padx=(10, 0))

        # Son KM
        km_frame = tk.Frame(dialog, bg=self.colors['bg_primary'])
        km_frame.pack(fill='x', padx=20, pady=5)
        tk.Label(km_frame, text="Son KM:", bg=self.colors['bg_primary'], 
                fg=self.colors['text_primary'], font=self.fonts['body']).pack(side='left', anchor='w')
        km_entry = tk.Entry(km_frame, bg=self.colors['bg_tertiary'], 
                           fg=self.colors['text_primary'], font=self.fonts['body'], relief='flat')
        km_entry.pack(side='right', fill='x', expand=True, padx=(10, 0))
        
        # Butonlar
        btn_frame = tk.Frame(dialog, bg=self.colors['bg_primary'])
        btn_frame.pack(fill='x', padx=20, pady=20)
        
        def save_reminder():
            try:
                plate = plate_combo.get()
                title = title_entry.get()
                description = desc_text.get('1.0', 'end-1c')
                due_date_val = date_entry.get_date().strftime('%Y-%m-%d') if CALENDAR_AVAILABLE else date_entry.get()
                due_km_val = km_entry.get()
                
                if not plate or not title:
                    messagebox.showerror("Hata", "Plaka ve Başlık boş olamaz!")
                    return

                data = (
                    plate,
                    'Manuel', # reminder_type
                    title,
                    description,
                    due_date_val,
                    int(due_km_val) if due_km_val else 0,
                    'Normal', # priority
                    'Aktif',  # status
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                )
                
                self.cursor.execute('''
                    INSERT INTO reminders (vehicle_plate, reminder_type, title, description,
                                         due_date, due_km, priority, status, created_date)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', data)
                
                self.conn.commit()
                messagebox.showinfo("Başarılı", "Hatırlatma eklendi!")
                dialog.destroy()
                self.update_status("Hatırlatma eklendi")
                self.show_reminders() # Hatırlatmalar listesini yenile
                
            except Exception as e:
                messagebox.showerror("Hata", f"Hatırlatma ekleme hatası: {str(e)}")
        
        save_btn = self.create_button(btn_frame, "💾 Kaydet", save_reminder, 
                                     self.colors['accent_green'])
        save_btn.pack(side='left', padx=5)
        
        cancel_btn = self.create_button(btn_frame, "❌ İptal", dialog.destroy, 
                                       self.colors['accent_red'])
        cancel_btn.pack(side='left', padx=5)

        self.root.wait_window(dialog) # Dialog kapanana kadar ana pencereyi beklet
    
    def toggle_theme(self):
        """Tema değiştir"""
        self.current_theme = 'acik' if self.current_theme == 'koyu' else 'koyu'
        self.colors = self.themes[self.current_theme]
        
        # Arayüzü yenile
        self.refresh_ui()
        self.update_status(f"Tema değiştirildi: {self.current_theme.title()}")
        self.save_setting('theme', self.current_theme) # Temayı kaydet
    
    def refresh_ui(self):
        """Arayüzü yenile"""
        # Mevcut sayfayı kaydet
        current_page = self.page_title.cget('text')
        
        # Ana container'ı yeniden oluştur
        for widget in self.root.winfo_children():
            widget.destroy()
        
        self.create_modern_ui()
        
        # Uygun sayfayı göster
        if current_page == "Araç Yönetimi":
            self.show_vehicles()
        elif current_page == "Bakım Kayıtları":
            self.show_maintenance()
        elif current_page == "Yakıt Kayıtları":
            self.show_fuel()
        elif current_page == "Analitik":
            self.show_analytics()
        elif current_page == "Raporlar":
            self.show_reports()
        elif current_page == "Hatırlatmalar":
            self.show_reminders()
        elif current_page == "Ayarlar":
            self.show_settings()
        else:
            self.show_dashboard()
    
    def refresh_all_data(self):
        """Tüm verileri yenile"""
        try:
            if hasattr(self, 'vehicle_tree'):
                self.load_vehicle_data()
            if hasattr(self, 'maintenance_tree'):
                self.load_maintenance_data()
            if hasattr(self, 'fuel_tree'):
                self.load_fuel_data()
            # Dashboard ve hatırlatmaları da yenile
            self.show_dashboard()
            self.show_reminders()
        except Exception as e:
            print(f"Veri yenileme hatası: {e}")
    
    def refresh_vehicles(self):
        """Araç listesini yenile"""
        self.load_vehicle_data()
        self.update_status("Araç listesi yenilendi")
    
    def lighten_color(self, color):
        """Rengi açık ton yap"""
        color_map = {
            self.themes['koyu']['accent_blue']: '#7BB3FF',
            self.themes['koyu']['accent_green']: '#5FBF6F',
            self.themes['koyu']['accent_orange']: '#FFB366',
            self.themes['koyu']['accent_red']: '#FF7A73',
            self.themes['koyu']['accent_purple']: '#D4A6FF',
            
            self.themes['acik']['accent_blue']: '#368CE7',
            self.themes['acik']['accent_green']: '#47A05A',
            self.themes['acik']['accent_orange']: '#E0525C',
            self.themes['acik']['accent_red']: '#E0525C',
            self.themes['acik']['accent_purple']: '#9B72E7'
        }
        return color_map.get(color, color)
    
    def clear_content(self):
        """İçeriği temizle"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
    
    def update_status(self, message):
        """Durum mesajını güncelle"""
        self.status_label.configure(text=message)
        # 3 saniye sonra "Hazır" yap
        self.root.after(3000, lambda: self.status_label.configure(text="Hazır"))

    def load_initial_data(self):
        """Başlangıçta ayarları yükle"""
        self.current_theme = self.get_setting('theme', 'koyu')
        self.colors = self.themes[self.current_theme]
        self.auto_backup_var.set(self.get_setting('auto_backup', 'True') == 'True')
        self.refresh_ui() # Temayı uygulamak için UI'ı yenile

    def save_setting(self, name, value):
        """Ayarı veritabanına kaydet"""
        self.cursor.execute('INSERT OR REPLACE INTO settings (setting_name, setting_value, updated_date) VALUES (?, ?, ?)',
                            (name, value, datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
        self.conn.commit()

    def get_setting(self, name, default_value):
        """Ayarı veritabanından al"""
        self.cursor.execute('SELECT setting_value FROM settings WHERE setting_name = ?', (name,))
        result = self.cursor.fetchone()
        return result[0] if result else default_value

    def save_auto_backup_setting(self):
        """Otomatik yedekleme ayarını kaydet"""
        self.save_setting('auto_backup', str(self.auto_backup_var.get()))
        self.update_status(f"Otomatik yedekleme ayarı kaydedildi: {self.auto_backup_var.get()}")

    def show_notifications(self):
        """Bildirimleri göster (şimdilik basit bir popup)"""
        messagebox.showinfo("Bildirimler", "Henüz yeni bildiriminiz yok.")
        self.update_status("Bildirimler kontrol edildi")

    def quick_search(self, event=None):
        """Hızlı arama fonksiyonu"""
        query = self.search_var.get().strip()
        if not query:
            messagebox.showinfo("Arama", "Lütfen bir arama terimi girin.")
            return

        results = []
        
        # Araçlarda ara
        self.cursor.execute("SELECT plate, model_year FROM vehicles WHERE plate LIKE ? OR model_year LIKE ?", (f'%{query}%', f'%{query}%'))
        for row in self.cursor.fetchall():
            results.append(f"Araç: {row[0]} ({row[1]})")

        # Bakım kayıtlarında ara
        self.cursor.execute("SELECT vehicle_plate, service_type, service_date FROM maintenance WHERE vehicle_plate LIKE ? OR service_type LIKE ?", (f'%{query}%', f'%{query}%'))
        for row in self.cursor.fetchall():
            results.append(f"Bakım: {row[0]} - {row[1]} ({row[2]})")

        # Yakıt kayıtlarında ara
        self.cursor.execute("SELECT vehicle_plate, fuel_station, fuel_date FROM fuel_records WHERE vehicle_plate LIKE ? OR fuel_station LIKE ?", (f'%{query}%', f'%{query}%'))
        for row in self.cursor.fetchall():
            results.append(f"Yakıt: {row[0]} - {row[1]} ({row[2]})")

        if results:
            messagebox.showinfo("Arama Sonuçları", "\n".join(results))
        else:
            messagebox.showinfo("Arama Sonuçları", "Eşleşen kayıt bulunamadı.")
        self.update_status(f"'{query}' için arama tamamlandı")

if __name__ == "__main__":
    root = tk.Tk()
    app = FiatStiloTurkish(root)
    root.mainloop()
