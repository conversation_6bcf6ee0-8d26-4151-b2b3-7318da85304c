import subprocess
import sys
import os

def run_program():
    """
    main_turkish.py dosyasını çalıştırır.
    """
    try:
        print("Program başlatılıyor...")
        # main_turkish.py dosyasının yolunu doğru bir şekilde belirtin
        script_path = os.path.join(os.path.dirname(__file__), 'main_turkish.py')
        subprocess.run([sys.executable, script_path], check=True)
    except FileNotFoundError:
        print("Hata: Python yorumlayıcısı veya main_turkish.py dosyası bulunamadı.")
        print("Lütfen Python'ın yüklü olduğundan ve PATH'e eklendiğinden emin olun.")
    except subprocess.CalledProcessError as e:
        print(f"Program çalışırken bir hata oluştu. Hata kodu: {e.returncode}")
        print(f"Detay: {e}")
    except Exception as e:
        print(f"Beklenmeyen bir hata oluştu: {e}")

if __name__ == "__main__":
    run_program()
