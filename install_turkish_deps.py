import subprocess
import sys

def install_dependencies():
    """
    requirements_turkish.txt dosyasındaki bağımlılıkları yükler.
    """
    try:
        print("Gerekli Python kütüphaneleri yükleniyor...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "scripts/requirements_turkish.txt"])
        print("Tüm kütüphaneler başarıyla yüklendi.")
    except subprocess.CalledProcessError as e:
        print(f"Hata: Kütüphaneler yüklenirken bir sorun oluştu. Hata kodu: {e.returncode}")
        print("Lütfen internet bağlantınızı kontrol edin veya pip'i güncelleyin.")
        print(f"Detay: {e}")
    except FileNotFoundError:
        print("Hata: 'pip' komutu bulunamadı. Python'ın PATH'e eklendiğinden emin olun.")
    except Exception as e:
        print(f"Beklenmeyen bir hata oluştu: {e}")

if __name__ == "__main__":
    install_dependencies()
